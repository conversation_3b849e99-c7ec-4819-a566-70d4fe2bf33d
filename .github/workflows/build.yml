name: Build

on:
  pull_request:
    branches:
      - dev

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        jdk-version:
          - 17
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: "adopt"
          java-version: ${{ matrix.jdk-version }}
          cache: "maven"
      - name: Compile
        run: |
          sed -i.bak '/<repositories>/,/<\/repositories>/d' pom.xml
          sed -i.bak '/<pluginRepositories>/,/<\/pluginRepositories>/d' pom.xml
          mvn -B compile --file pom.xml
