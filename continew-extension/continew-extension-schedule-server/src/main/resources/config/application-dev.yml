server:
  port: 8001

--- ### 数据源配置
spring.datasource:
  url: jdbc:mysql://${DB_HOST:127.0.0.1}:${DB_PORT:3306}/${DB_NAME:continew_admin_job}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true&rewriteBatchedStatements=true&autoReconnect=true&maxReconnects=10&failOverReadOnly=false&allowPublicKeyRetrieval=true
  username: ${DB_USER:root}
  password: ${DB_PWD:12345678}
  driver-class-name: com.mysql.cj.jdbc.Driver
#  # PostgreSQL 配置
#  url: jdbc:postgresql://${DB_HOST:127.0.0.1}:${DB_PORT:3306}/${DB_NAME:continew_admin_job}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true&rewriteBatchedStatements=true&autoReconnect=true&maxReconnects=10&failOverReadOnly=false
#  username: ${DB_USER:root}
#  password: ${DB_PWD:123456}
#  driver-class-name: org.postgresql.Driver
## Liquibase 配置
spring.liquibase:
  # 是否启用
  enabled: true
  # 配置文件路径
  change-log: classpath:/db/changelog/db.changelog-master.yaml

--- ### Snail Job 服务端配置
snail-job:
  # Netty 端口
  netty-port: 1788
  # 合并日志默认保存天数
  merge-Log-days: 1
  # 合并日志默认的条数
  merge-Log-num: 500
  # 配置日志保存时间（单位：天）
  log-storage: 90
  # 配置每批次拉取重试数据的大小
  retry-pull-page-size: 100
  # 配置一个客户端每秒最多接收的重试数量指令
  limiter: 10
  # 配置号段模式下的步长
  step: 100
  # bucket 的总数量
  bucket-total: 128
  # Dashboard 任务容错天数
  summary-day: 7
  # 配置负载均衡周期时间
  load-balance-cycle-time: 10
  ## 回调配置
  callback:
    # 回调 uniqueId 前缀
    prefix: CB
    # 配置回调的最大执行次数
    max-count: 288
    # 配置回调触发的间隔时间
    trigger-interval: 900

--- ### 日志配置
logging:
  level:
    com.aizuda.snailjob: DEBUG
  file:
    path: ./logs