package top.continew.admin.biz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

@Configuration
@ConfigurationProperties(prefix = "telegram")
@Data
public class TelegramChatIdConfig {

    private Long rechargeChatId;

    private Long monitorChatId;

    private Long businessChatId;

    private Long productionChatId;

    private Long developmentChatId;

    private Long operationChatId;

    private Long adAccountNotifyChatId;

    private Long financeChatId;

    private Long clientNotifyChatId;

    public boolean isValidChatId(Long chatId) {
        return Objects.equals(chatId, rechargeChatId) || Objects.equals(chatId, monitorChatId) || Objects.equals(chatId, businessChatId) || Objects.equals(chatId, productionChatId) || Objects.equals(chatId, operationChatId) || Objects.equals(chatId, adAccountNotifyChatId);
    }
}
