/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AdAccountExcelStatus {
    FORSALE("待售", AdAccountStatusEnum.NORMAL, AdAccountKeepStatusEnum.SUCCESS, AdAccountSaleStatusEnum.WAIT),
    SALEOUT("已售", AdAccountStatusEnum.NORMAL, AdAccountKeepStatusEnum.SUCCESS, AdAccountSaleStatusEnum.SALT),
    STOP("停用", AdAccountStatusEnum.BANNED, AdAccountKeepStatusEnum.SUCCESS, AdAccountSaleStatusEnum.WAIT),
    RECYCLE("回收", AdAccountStatusEnum.NORMAL, AdAccountKeepStatusEnum.SUCCESS, AdAccountSaleStatusEnum.RECYCLE),
    INTERNAL_TRANSFER("内部转户", AdAccountStatusEnum.NORMAL, AdAccountKeepStatusEnum.SUCCESS, AdAccountSaleStatusEnum.INTERNAL_TRANSFER),;

    @EnumValue
    @JsonValue
    private final String name;

    private final AdAccountStatusEnum adAccountStatus;

    private final AdAccountKeepStatusEnum keepingAdAccountStatus;

    private final AdAccountSaleStatusEnum saleStatus;

    public static AdAccountExcelStatus getAdAccountExcelStatus(String name) {
        for (AdAccountExcelStatus adAccountExcelStatus : AdAccountExcelStatus.values()) {
            if (adAccountExcelStatus.getName().equals(name)) {
                return adAccountExcelStatus;
            }
        }
        return null;
    }

}