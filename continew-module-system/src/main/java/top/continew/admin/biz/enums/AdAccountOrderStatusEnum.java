/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum AdAccountOrderStatusEnum implements BaseEnum<Integer> {
    PENDING(1, "待处理"), PROCESS(2, "处理中"), AUTH_COMPLETED(3, "授权完成"), CANCEL(4, "已取消"), RECYCLE(5, "回收"),
    INVALID(6, "作废"), SELF_AUTH_FAIL(7, "授权失败"), RECEIVE_FAIL(8, "接收失败");

    private final Integer value;
    private final String description;
}
