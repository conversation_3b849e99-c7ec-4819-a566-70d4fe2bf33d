/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum AdAccountSaleStatusEnum implements BaseEnum<Integer> {
    WAIT(1, "待出售"), SALT(2, "已出售"), RECYCLE(3, "已回收"), INTERNAL_TRANSFER(4, "内部转户"), INVALID(5, "作废"),
    SALEING(6, "出售中"), LOCK(9, "特供");

    private final Integer value;
    private final String description;
}
