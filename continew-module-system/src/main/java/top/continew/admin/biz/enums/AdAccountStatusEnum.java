/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

import java.util.Objects;

@Getter
@RequiredArgsConstructor
public enum AdAccountStatusEnum implements BaseEnum<Integer> {
    NORMAL(1, "正常"), BANNED(2, "停用"), ABNORMAL(3, "异常"), PENDING_CLOSURE(100, "关闭中"), CLOSED(101, "已关闭");

    private final Integer value;
    private final String description;

    public static AdAccountStatusEnum getEnum(Integer value) {
        for (AdAccountStatusEnum adAccountStatusEnum : values()) {
            if (Objects.equals(adAccountStatusEnum.getValue(), value)) {
                return adAccountStatusEnum;
            }
        }
        return null;
    }
}
