package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum AdAccountUnusableReasonEnum implements BaseEnum<Integer> {
    BM_BANNED(1, "BM被封"), BM_KICKED_BY_PERMISSION(2, "BM权限被踢"), BM_KILL(3, "BM被扫"),
    SELF_BANNED(4, "广告户停用"), AD_ACCOUNT_RESTRICTED(5, "广告户受限"), UN_SPENT(6, "二次不消耗"),
    AD_REJECT(7, "广告一直被拒"), INVALID_PAYMENT(8, "无法支付"), CARD_RESTRICTED(9, "卡受限"),
    FREEZE_FAILED(10, "冻结失败"), ORIGIN_ACCOUNT_LOST(11, "原号丢失"), ALL_PERSON_ACCOUNT_BANNER(12, "所有个号被封"),
    ALL_PERSON_ACCOUNT_BANNER_RESTRICTED(13, "所有个号受限"), BM_RESTRICTED(14, "BM受限"), REJECT_PAID(15, "拒付"),
    PREPAY_ACCOUNT(16, "预充户");

    private final Integer value;
    private final String description;

    public static AdAccountUnusableReasonEnum getByBmBanReason(BusinessManagerBannedReasonEnum bmBanReason) {
        AdAccountUnusableReasonEnum adAccountUnusableReason = AdAccountUnusableReasonEnum.BM_BANNED;
        if (BusinessManagerBannedReasonEnum.BANNED_BY_KILL.equals(bmBanReason)) {
            adAccountUnusableReason = AdAccountUnusableReasonEnum.BM_KILL;
        } else if (BusinessManagerBannedReasonEnum.KICKED_BY_PERMISSION.equals(bmBanReason)) {
            adAccountUnusableReason = AdAccountUnusableReasonEnum.BM_KICKED_BY_PERMISSION;
        }
        return adAccountUnusableReason;
    }
}