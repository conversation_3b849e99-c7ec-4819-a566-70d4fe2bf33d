package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum BusinessManagerBannedReasonEnum implements BaseEnum<Integer> {
    KICKED_BY_PERMISSION(1, "权限被踢"), BANNED_BY_RECHARGE(2, "充值被封"), BANNED_BY_INVITATION(3, "邀请被封"),
    BANNED_BY_OPENING(4, "打开被封"), BANNED_BY_CLAIMING(5, "认领被封"), BANNED_BY_RENAMING(6, "改名被封"),
    BANNED_BY_BMID_AUTH(7, "bmid授权被封"), BANNED_BY_ASSET_ASSIGNMENT(8, "资产指定被封"), BANNED_SYSTEM(9, "系统检测"),
    BANNED_BY_REMOVE_USER(10, "踢人被封"), BANNED_BY_KILL(11, "被扫");

    private final Integer value;
    private final String description;
}