/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum BusinessManagerTypeEnum implements BaseEnum<Integer> {
    BM5(1, "bm5"), BM50(2, "bm50"), BM250(3, "bm250"), BM2500(4, "bm2500"), BM10000(5, "bm10000"), TK(6, "TK户"),
    BM1(7, "BM1"), BILL_ACCOUNT(8, "账单户"), BM3(9, "bm3"), BM13000(10, "bm13000"), BM3000(11, "bm3000"),
    BM4000(12, "BM4000"), BM5000(13, "BM5000"), BM10(14, "BM10"),
    ;

    private final Integer value;
    private final String description;

    public static BusinessManagerTypeEnum getByPurchaseOrderTYpe(PurchaseOrderTypeEnum purchaseOrderTypeEnum) {
        switch (purchaseOrderTypeEnum) {
            case BM1, BM1_ENTERPRISE_AUTH -> {
                return BM1;
            }
            case BM3 -> {
                return BM3;
            }
            case BM5 -> {
                return BM5;
            }
            case BM50 -> {
                return BM50;
            }
            case BM250 -> {
                return BM250;
            }
            case BM2500 -> {
                return BM2500;
            }
            case BM3000 -> {
                return BM3000;
            }
            case BM10000 -> {
                return BM10000;
            }
            case BM13000 -> {
                return BM13000;
            }
            case BM4000 -> {
                return BM4000;
            }
            case BM5000 -> {
                return BM5000;
            }
            case BM10 -> {
                return BM10;
            }
        }
        return null;
    }
}
