/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum ClearOrderCardStatusEnum implements BaseEnum<Integer> {
    WAIT(1, "待处理"), SUCCESS(2, "成功"), FAIL(3, "失败"), PART_SUCCESS(4, "部分成功"), REVOKE(9, "已撤销");

    private final Integer value;
    private final String description;

    public static ClearOrderCardStatusEnum of(Integer value) {
        for (ClearOrderCardStatusEnum e : ClearOrderCardStatusEnum.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return null;
    }
}
