/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum CustomerBalanceTypeEnum implements BaseEnum<Integer> {
    SYSTEM(1, "打款"), RECHARGE_FEE(2, "充值服务费"), AD_ACCOUNT_RECHARGE(3, "广告户充值"),
    AD_ACCOUNT_CLEAR(4, "广告户清零"), AD_ACCOUNT_REDUCE(5, "广告户减款"), AD_ACCOUNT_BUY(6, "开户费"),
    MIGRATE(7, "资金转移"), DEDUCE(8, "扣款"), AD_ACCOUNT_REFUND(9, "开户费退款"),
    RECHARGE_FEE_REFUND(10, "服务费退款");

    private final Integer value;
    private final String description;
}
