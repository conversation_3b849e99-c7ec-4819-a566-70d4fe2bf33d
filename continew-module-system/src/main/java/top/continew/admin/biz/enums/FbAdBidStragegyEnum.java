/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

/**
 * Facebook 广告竞价策略枚举
 */
@Getter
@RequiredArgsConstructor
public enum FbAdBidStragegyEnum implements BaseEnum<String> {
    /**
    * LOWEST_COST_WITHOUT_CAP：Facebook 会代表您自动竞价，为您带来最低费用成效。
    * 根据需要自动提高您的有效竞价，以根据您给定 optimization_goal 获得您想要的结果。
    * 如果您选择价值作为 optimization_goal，我们会在广告管理工具中显示最高价值作为您的竞价策略。
    */
    LOWEST_COST_WITHOUT_CAP("LOWEST_COST_WITHOUT_CAP", "最高数量"),
    /**
     * COST_CAP：在我们努力满足您设置的单次成效费用的同时，尽可能获得尽可能多的结果。
     * 注意：我们无法保证遵守成本上限限制。
     *
     * 如果你看到“单次成效费用目标”或“目标 CPA”，它们都指的是 COST_CAP 策略。
     */
    COST_CAP("COST_CAP", "单次成效费用目标"),
    /**
     * LOWEST_COST_WITH_MIN_ROAS：用于价值优化的特定出价选项。
     * 您必须指定一个 roas_average_floor，即希望从广告支出中获得的最低回报。
     */
    LOWEST_COST_WITH_MIN_ROAS("LOWEST_COST_WITH_MIN_ROAS", "广告花费回报目标"),
    /**
     * LOWEST_COST_WITH_BID_CAP：我们会自动为您竞价并获得最低的成本。
     * 我们会根据需要自动提高您的出价，以获得您想要的结果，但不会超过您指定的限制。
     */
    LOWEST_COST_WITH_BID_CAP("LOWEST_COST_WITH_BID_CAP", "竞价上限");

    ;

    private final String value;
    private final String description;
}