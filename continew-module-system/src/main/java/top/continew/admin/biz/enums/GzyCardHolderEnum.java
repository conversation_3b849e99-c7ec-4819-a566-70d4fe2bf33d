package top.continew.admin.biz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/3/24 11:45
 */
@AllArgsConstructor
public enum GzyCardHolderEnum {
    USER1("CH1897136133075542016", "really"),
    USER2("CH1898709145373544448", "jinx"),
    USER3("CH1900563274895118336", "hebai"),
    USER4("CH1902667442527076352", "umi1"),
    USER6("CH1903433452767088640", "hans"),
    USER5("CH1903733583357878272", "common");

    @Getter
    private String holderId;
    @Getter
    private String holderName;


    public static String getHolderName(String holderId) {
        for (GzyCardHolderEnum cardHolderEnum : GzyCardHolderEnum.values()) {
            if (cardHolderEnum.getHolderId().equals(holderId)) {
                return cardHolderEnum.getHolderName();
            }
        }

        return "未知";
    }
}
