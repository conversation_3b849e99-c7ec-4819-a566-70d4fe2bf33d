package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

import java.util.Objects;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/5/19 17:43
 */
@Getter
@RequiredArgsConstructor
public enum LeadStatusEnum implements BaseEnum<Integer> {
    TO_FOLLOW_UP(1, "待跟进"),
    FOLLOWING_UP(2, "跟进中"),
    LONG_TERM_FOLLOW_UP(3, "长期跟进"),
    CREATE_OPPORTUNITY(4, "创建商机"),
    INVALID(5, "无效");

    private final Integer value;
    private final String description;

    public static LeadStatusEnum getEnum(Integer value) {
        for (LeadStatusEnum leadStatusEnum : values()) {
            if (Objects.equals(leadStatusEnum.getValue(), value)) {
                return leadStatusEnum;
            }
        }
        return null;
    }
}
