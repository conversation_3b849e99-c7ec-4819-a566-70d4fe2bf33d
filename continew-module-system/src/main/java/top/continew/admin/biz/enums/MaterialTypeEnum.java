/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum MaterialTypeEnum implements BaseEnum<Integer> {
    BM5(1, "BM5"), BIG_BLACK_ACCOUNT(2, "大黑号"), SECOND_SOLUTION_ACCOUNT(3, "二解号"),
    THREE_SOLUTION_ACCOUNT(4, "三解号"), SHORT_ID_ACCOUNT(5, "短链号"), OLD_AMERICAN_ACCOUNT(6, "美国老号"),
    OUTLOOK_EMAIL(8, "outlook邮箱"), BM(9, "BM"), PUBLIC_HOME_PAGE(10, "公共主页"), SMS_PLATFORM(11, "接码平台充值"),
    TRX_BUY(12, "购买TRX"), PROXY(13, "代理IP"), ADS_POWER(14, "Adspower费用"), BILL_ACCOUNT(15, "账单户"),
    CLOUD_SERVER(16, "FB服务器"), CHROME_PLUGIN(17, "越南插件"), BM_2500(18, "BM2500分享户"), BM250(19, "BM250"),
    BM10000(20, "BM10000分享户"), BM1(21, "BM1"), ACCOUNT_WITH_COMPANY_AUTH(22, "企业认证户"),
    BM13000(23, "BM13000分享户"), BM3000(24, "BM3000分享户"), BM3(25, "BM3"), BM10(26, "BM10"), BM50(27, "BM50"),
    BM4000(28, "BM4000"), BM5000(29, "BM5000"), BM1_ENTERPRISE_AUTH(30, "企业认证BM1"),CLONE_BROWSER(80, "ClonBrowser费用"),
    GOOGLE_AD_ACCOUNT(81, "GOOGLE账号"), OTHER(99, "其他");

    private final Integer value;
    private final String description;

    public static MaterialTypeEnum getByPurchaseOrderType(PurchaseOrderTypeEnum purchaseOrderTypeEnum) {
        switch (purchaseOrderTypeEnum) {
            case BIG_BLACK_ACCOUNT -> {
                return BIG_BLACK_ACCOUNT;
            }
            case THREE_APPEAR_ACCOUNT -> {
                return THREE_SOLUTION_ACCOUNT;
            }
            case BM1 -> {
                return BM1;
            }
            case BM3 -> {
                return BM3;
            }
            case BM5 -> {
                return BM5;
            }
            case BM10 -> {
                return BM10;
            }
            case BM50 -> {
                return BM50;
            }
            case BM250 -> {
                return BM250;
            }
            case BM2500 -> {
                return BM_2500;
            }
            case BM3000 -> {
                return BM3000;
            }
            case BM10000 -> {
                return BM10000;
            }
            case BM13000 -> {
                return BM13000;
            }
            case BM4000 -> {
                return BM4000;
            }
            case BM5000 -> {
                return BM5000;
            }
            case BM1_ENTERPRISE_AUTH -> {
                return BM1_ENTERPRISE_AUTH;
            }
            case HISTORY_BILLING_ACCOUNT -> {
                return BILL_ACCOUNT;
            }
        }
        return null;
    }
}
