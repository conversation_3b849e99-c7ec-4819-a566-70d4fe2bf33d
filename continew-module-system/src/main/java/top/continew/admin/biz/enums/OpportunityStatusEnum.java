package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

import java.util.Objects;

@Getter
@RequiredArgsConstructor
public enum OpportunityStatusEnum implements BaseEnum<Integer> {
    TO_FOLLOW_UP(1, "待跟进"),
    FOLLOWING_UP(2, "跟进中"),
    LONG_TERM_FOLLOW_UP(3, "长期跟进"),
    WON(4, "赢单"),
    LOST(5, "流失");

    private final Integer value;
    private final String description;

    public static OpportunityStatusEnum getEnum(Integer value) {
        for (OpportunityStatusEnum statusEnum : values()) {
            if (Objects.equals(statusEnum.getValue(), value)) {
                return statusEnum;
            }
        }
        return null;
    }
}