package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum PersonalAccoutAfterSaleStatusEnum implements BaseEnum<Integer> {
    NO(0, "无需售后"), WAIT(1, "待售后"), PROCESS(2, "售后中"), SUCCESS(3, "已售后"), INVALID(4, "无法售后");

    private final Integer value;
    private final String description;
}
