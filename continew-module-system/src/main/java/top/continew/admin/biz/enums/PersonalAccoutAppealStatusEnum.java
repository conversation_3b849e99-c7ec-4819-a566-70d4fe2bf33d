package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum PersonalAccoutAppealStatusEnum implements BaseEnum<Integer> {
    NO_APPEAL(1, "未申诉"), PROCESS(2, "申诉中"), SUCCESS(3, "申诉成功"), FAIL(4, "申诉失败");

    private final Integer value;
    private final String description;
}
