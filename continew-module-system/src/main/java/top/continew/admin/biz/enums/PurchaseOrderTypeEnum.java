/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum PurchaseOrderTypeEnum implements BaseEnum<Integer> {
    BIG_BLACK_ACCOUNT(1, "大黑号"), THREE_APPEAR_ACCOUNT(2, "三解号"), BM1(3, "BM1"), BM3(4, "BM3"), BM5(5, "BM5"),
    BM10(6, "BM10"), BM50(7, "BM50"), BM250(8, "BM250"), BM2500(9, "BM2500"), BM3000(10, "BM3000"),
    BM10000(11, "BM10000"), BM13000(12, "BM13000"), BM4000(13, "BM4000"), BM5000(14, "BM5000"),
    BM1_ENTERPRISE_AUTH(15, "企业认证BM1"), HISTORY_BILLING_ACCOUNT(50, "个人账单户");

    private final Integer value;
    private final String description;
}
