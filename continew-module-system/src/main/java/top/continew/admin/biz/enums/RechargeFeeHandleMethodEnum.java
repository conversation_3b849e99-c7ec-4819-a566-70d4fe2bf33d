/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum RechargeFeeHandleMethodEnum implements BaseEnum<Integer> {
    REDUCE_FROM_RECHARGE_AMOUNT(1, "打款时从本金中扣除"), RECHARGE_INCLUDE_FEE(2, "打款时包含手续费"), NO_FEE(9, "自定义手续费");

    private final Integer value;
    private final String description;

    public static RechargeFeeHandleMethodEnum getByValue(Integer value) {
        for (RechargeFeeHandleMethodEnum e : RechargeFeeHandleMethodEnum.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return null;
    }
}
