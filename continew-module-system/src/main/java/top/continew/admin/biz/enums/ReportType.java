/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ReportType {

    BILLING_ACCOUNT_INFORMATION_UTILS_CREATE_ACCOUNT_MUTATION("BillingAccountInformationUtilsCreateAccountMutation", "创建支付信息"),
    BILLING_SAVE_CARD_CREDENTIAL_STATE_MUTATION("BillingSaveCardCredentialStateMutation", "添加借记卡或信用卡"),
    ADS_ACCOUNT_STORE_NEW_SOURCE_SERVER_QUERY("AdsAccountStoreNewSourceServerQuery", "广告账号状态查询"),
    ADS_ACCOUNT_DATA_LOADER_PRELOADER("AdsAccountDataLoaderPreloader", "采集广告户信息"), LOCK("Locked", "帐号被锁定"),
    BILLING_HUB_PAYMENT_SETTINGS_VIEW_QUERY("BillingHubPaymentSettingsViewQuery", "账单户设置状态查询"),
    BILLING_HUB_PAYMENT_SETTINGS_PAYMENT_METHODS_LIST_QUERY("BillingHubPaymentSettingsPaymentMethodsListQuery", "付款方式查询"),
    BIZ_KIT_SETTINGS_CLAIM_AD_ACCOUNT_MUTATION("BizKitSettingsClaimAdAccountMutation", "认领广告"),
    CAMPAIGNS_PAGE_LOADED("CampaignsPageLoaded", "广告系列加载"),
    BILLING_ACCOUNT_INFORMATION_UTILS_UPDATE_ACCOUNT_MUTATION("BillingAccountInformationUtilsUpdateAccountMutation", "更新支付信息"),
    SCREENSHOT_AFTER_UPDATE_LIMIT("ScreenshotAfterUpdateLimit", "修改限额截图"), LOGOUT("Logout", "账号登出"),
    BILLING_ACCOUNT_INFORMATION_SCREEN_QUERY("BillingAccountInformationScreenQuery", "账单公司信息查询"),
    USE_BILLING_RESET_SPEND_MUTATION("useBillingResetSpendMutation", "重置花费"),
    USE_BILLING_UPDATE_ACCOUNT_SPEND_LIMIT_SCREEN_MUTATION("useBillingUpdateAccountSpendLimitScreenMutation", "修改限额"),
    MOVE_AD_ACCOUNT_TO_BM("MoveAdAccountToBM", "插件认领广告户");

    @EnumValue
    @JsonValue
    private final String name;
    private final String label;

    public static ReportType formName(String name) {
        for (ReportType reportType : values()) {
            if (reportType.getName().equals(name)) {
                return reportType;
            }
        }
        return null;
    }

}
