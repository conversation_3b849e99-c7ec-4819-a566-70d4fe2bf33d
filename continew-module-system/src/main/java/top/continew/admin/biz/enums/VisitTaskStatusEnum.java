package top.continew.admin.biz.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

import java.util.Objects;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/5/19 17:43
 */
@Getter
@RequiredArgsConstructor
public enum VisitTaskStatusEnum implements BaseEnum<Integer> {
    TO_DO(1, "待处理"),
    PENDING(2, "处理中"),
    FINISH(3, "已完成"),
    CLOSE(4, "已关闭");

    private final Integer value;
    private final String description;

    public static VisitTaskStatusEnum getEnum(Integer value) {
        for (VisitTaskStatusEnum row : values()) {
            if (Objects.equals(row.getValue(), value)) {
                return row;
            }
        }
        return null;
    }
}
