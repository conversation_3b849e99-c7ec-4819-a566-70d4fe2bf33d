package top.continew.admin.biz.event;

import lombok.Data;
import org.springframework.context.ApplicationEvent;
import top.continew.admin.biz.enums.CardPlatformEnum;

public class AdAccountBindCardEvent extends ApplicationEvent {

    public AdAccountBindCardEvent(Object source) {
        super(source);
    }

    @Data
    public static class AdAccountBindCardModel {

        private String cardNumber;

        private String platformAdId;

        private CardPlatformEnum cardPlatform;

        public AdAccountBindCardModel() {
        }

        public AdAccountBindCardModel(String cardNumber, String platformAdId, CardPlatformEnum cardPlatform) {
            this.cardNumber = cardNumber;
            this.platformAdId = platformAdId;
            this.cardPlatform = cardPlatform;
        }
    }
}
