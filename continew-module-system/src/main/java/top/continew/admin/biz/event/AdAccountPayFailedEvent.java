package top.continew.admin.biz.event;

import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;

public class AdAccountPayFailedEvent extends ApplicationEvent {

    private String remark;

    private BigDecimal amount;

    public AdAccountPayFailedEvent(Object source, String remark, BigDecimal amount) {
        super(source);
        this.remark = remark;
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public BigDecimal getAmount() {
        return amount;
    }
}
