/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class BusinessManagerExcelData {

    @ExcelProperty("购买日期")
    private String purchaseDate;
    @ExcelProperty("渠道")
    private String channel;
    @ExcelProperty("物料类型")
    private String materialType;
    @ExcelProperty("浏览器")
    private String browser;
    @ExcelProperty("账号信息")
    private String accountInfo;
    @ExcelProperty("卡台")
    private String cardPlatform;
    @ExcelProperty("BM5绑定卡号")
    private String bm5BindCardNumber;
    @ExcelProperty("是否已剔除其他人员")
    private String isExcluded;
    @ExcelProperty("分配人员")
    private String assignee;
    @ExcelProperty("状态")
    private String status;
    @ExcelProperty("接入者")
    private String accessPerson;
    @ExcelProperty("使用者")
    private String user;
    @ExcelProperty("备注")
    private String remark;

}
