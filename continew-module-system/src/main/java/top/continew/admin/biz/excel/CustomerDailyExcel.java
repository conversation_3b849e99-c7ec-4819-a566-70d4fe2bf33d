/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CustomerDailyExcel {

    @ExcelProperty("日期")
    private String date;

    @ExcelProperty("归属客户")
    private String customer;

    @ExcelProperty("广告户ID")
    private String adAccountId;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("账户充值金额")
    private BigDecimal rechargeAmount;

    @ExcelProperty("打款")
    private BigDecimal payment;

    @ExcelProperty("服务费")
    private BigDecimal serviceFee;

    @ExcelProperty("开户费")
    private BigDecimal openingFee;

    @ExcelProperty("充值广告户金额")
    private BigDecimal rechargeAdAccountAmount;

    @ExcelProperty("取款金额")
    private BigDecimal withdrawalAmount;

    @ExcelProperty("调账")
    private BigDecimal adjustment;

    @ExcelProperty("退款")
    private BigDecimal refund;

    @ExcelProperty("备注")
    private String remark;
}
