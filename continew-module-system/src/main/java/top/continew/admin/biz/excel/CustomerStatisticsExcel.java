package top.continew.admin.biz.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CustomerStatisticsExcel {

    @ExcelProperty("客户名称")
    @ColumnWidth(20)
    private String name;

    @ExcelProperty("关联商务")
    @ColumnWidth(20)
    private String businessUserName;

    @ExcelProperty("总户数")
    @ColumnWidth(15)
    private Integer totalAccount;

    @ExcelProperty("死户数量")
    @ColumnWidth(15)
    private Integer deadAccount;

    @ExcelProperty("死户率(%)")
    @ColumnWidth(15)
    private BigDecimal deadRate;

    @Schema(description = "坑位费")
    @ExcelProperty("坑位费")
    @ColumnWidth(15)
    private BigDecimal accountCost;

    @Schema(description = "开户费")
    @ExcelProperty("开户费")
    @ColumnWidth(15)
    private BigDecimal payAmount;


    @ExcelProperty("FB消耗")
    @ColumnWidth(15)
    private BigDecimal totalSpend;

    @ExcelProperty("卡台消耗")
    @ColumnWidth(15)
    private BigDecimal cardSpent;

    @Schema(description = "平均FB户消耗")
    @ExcelProperty("平均FB户消耗")
    @ColumnWidth(15)
    private BigDecimal averageFbSpend;

    @Schema(description = "平均卡台户消耗")
    @ExcelProperty("平均卡台户消耗")
    @ColumnWidth(15)
    private BigDecimal averageCardSpend;

    @ExcelProperty("总打款")
    @ColumnWidth(15)
    private BigDecimal totalPaid;

    @ExcelProperty("总占用资金")
    @ColumnWidth(15)
    private BigDecimal occupiedAmount;

    @ExcelProperty("总充值")
    @ColumnWidth(15)
    private BigDecimal totalRecharge;

    @ExcelProperty("平均户充值")
    @ColumnWidth(15)
    private BigDecimal averageRecharge;

    @ExcelProperty("卡台余额")
    @ColumnWidth(15)
    private BigDecimal cardBalance;
}
