package top.continew.admin.biz.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class FbAccountBitExcelData {

    @ExcelProperty("窗口名称")
    private String windowName;
    @ExcelProperty("分组")
    private String group;
    @ExcelProperty("账号平台")
    private String accountPlatform;
    @ExcelProperty("用户名")
    private String username;
    @ExcelProperty("密码")
    private String password;
    @ExcelProperty("2FA秘钥")
    private String twoFactorSecret;
    @ExcelProperty("Cookie")
    private String cookie;
    @ExcelProperty("打开指定网址")
    private String openUrl;
    @ExcelProperty("代理类型")
    private String proxyType;
    @ExcelProperty("代理信息")
    private String proxyInfo;
    @ExcelProperty("IP查询渠道")
    private String ipQueryChannel;
    @ExcelProperty("刷新URL")
    private String refreshUrl;
    @ExcelProperty("国家/地区（针对动态IP）")
    private String country;
    @ExcelProperty("州/省（针对动态IP）")
    private String state;
    @ExcelProperty("城市（针对动态IP）")
    private String city;
    @ExcelProperty("窗口备注")
    private String windowNote;
    @ExcelProperty("User Agent")
    private String userAgent;
    @ExcelProperty("窗口尺寸")
    private String windowSize;

}
