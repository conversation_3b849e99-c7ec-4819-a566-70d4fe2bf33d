/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class FbAccountExcelData {

    @ExcelProperty("name")
    private String name;

    @ExcelProperty("remark")
    private String remark;

    @ExcelProperty("tab")
    private String tab;

    @ExcelProperty("platform")
    private String platform;

    @ExcelProperty("username")
    private String username;

    @ExcelProperty("password")
    private String password;

    @ExcelProperty("fakey")
    private String fakey;

    @ExcelProperty("cookie")
    private String cookie;

    @ExcelProperty("proxytype")
    private String proxyType;

    @ExcelProperty("ipchecker")
    private String ipChecker;

    @ExcelProperty("proxy")
    private String proxy;

    @ExcelProperty("proxyurl")
    private String proxyUrl;

    @ExcelProperty("proxyid")
    private String proxyId;

    @ExcelProperty("ip")
    private String ip;

    @ExcelProperty("countrycode")
    private String countryCode;

    @ExcelProperty("regioncode")
    private String regionCode;

    @ExcelProperty("citycode")
    private String cityCode;

    @ExcelProperty("ua")
    private String ua;

    @ExcelProperty("resolution")
    private String resolution;
}
