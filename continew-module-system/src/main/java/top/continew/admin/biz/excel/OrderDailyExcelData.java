/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderDailyExcelData {

    @ExcelProperty("下户日期")
    private String date;

    @ExcelProperty("时区")
    private String timeZone;

    @ExcelProperty("广告户名称")
    private String adAccountName;

    @ExcelProperty("客户BM")
    private String customerBm;

    @ExcelProperty("账户ID")
    private String accountId;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("总充值")
    private BigDecimal totalRecharge;

    @ExcelProperty("清零时间")
    private String cleanTime;

    @ExcelProperty("备注")
    private String remark;
}
