/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ProducerRecordsExcelData {
    @ExcelProperty("购买时间以及数量")
    private String string;

    @ExcelProperty("操作时间")
    private String operationTime;

    @ExcelProperty("操作人员")
    private String operator;
    @ExcelProperty("个号地区")
    private String personalArea;
    @ExcelProperty("原浏览器")
    private String originalBrowser;
    @ExcelProperty("操作浏览器")
    private String opsBrowser;
    @ExcelProperty("时区")
    private String timeZone;
    @ExcelProperty("BM5")
    private String bm5;

    @ExcelProperty("广告户ID")
    private String adAccountId;

    @ExcelProperty("海连卡号")
    private String hailianCardNumber;

    @ExcelProperty("汇通卡号")
    private String huitongCardNumber;

    @ExcelProperty("CV卡台卡号")
    private String cvCardNumber;

    @ExcelProperty("账号状态")
    private String status;

    @ExcelProperty("复核状态")
    private String reviewStatus;

    @ExcelProperty("申诉情况")
    private String appeal;

    @ExcelProperty("客户BM")
    private String customerBM;

    @ExcelProperty("首次售出日期")
    private String firstSaleDate;

    @ExcelProperty("归属客户")
    private String customer;

    @ExcelProperty("授权人")
    private String authorizer;

    @ExcelProperty("是否取款")
    private String withdrawal;

    @ExcelProperty("商务")
    private String business;

    @ExcelProperty("合作方式")
    private String cooperationMethod;

    @ExcelProperty("备注")
    private String remark;
}