/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import top.continew.admin.biz.excel.ProducerRecordsExcelData;
import top.continew.admin.biz.service.AdAccountService;

import java.util.List;
import java.util.Set;

@Slf4j
public class ProducerRecordsListener extends AnalysisEventListener<ProducerRecordsExcelData> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    /**
     * 缓存的数据
     */
    private List<ProducerRecordsExcelData> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    private final AdAccountService adAccountService;

    private Set<String> adAccountIdSet;

    public ProducerRecordsListener(AdAccountService adAccountService, Set<String> adAccountIdSet) {
        this.adAccountService = adAccountService;
        this.adAccountIdSet = adAccountIdSet;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(ProducerRecordsExcelData data, AnalysisContext context) {
        log.info("【广告户导入】解析到一条数据:{}", JSON.toJSONString(data));
        if (StringUtils.isNumeric(data.getAdAccountId())) {
            cachedDataList.add(data);
        }
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
        log.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());
        adAccountService.saveExcelData(cachedDataList);
        log.info("存储数据库成功！");
    }
}