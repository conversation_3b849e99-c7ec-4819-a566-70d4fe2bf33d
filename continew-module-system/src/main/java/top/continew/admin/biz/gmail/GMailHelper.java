package top.continew.admin.biz.gmail;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import top.continew.admin.biz.utils.ThreadPoolHelper;
import javax.mail.search.*;
import java.io.IOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.*;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GMailHelper {
    private static final String VERIFICATION_CODE_REDIS_KEY = "gmail:verification:codes";
    private static final String PROCESSED_MESSAGES_KEY = "gmail:processed:messages";
    private static final Pattern VERIFICATION_CODE_PATTERN = Pattern.compile("\\b\\d{6}\\b");
    private static final Pattern CARD_LAST_FOUR_PATTERN = Pattern.compile("(\\d+\\*+\\d+)");
    private static final String HOST = "imap.gmail.com";

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Resource
    private GMailProperties gmailProperties;

    private void maintainCacheSize() {
        // 维护验证码列表大小
        Long codesSize = stringRedisTemplate.opsForList().size(VERIFICATION_CODE_REDIS_KEY);
        if (codesSize != null && codesSize > gmailProperties.getMaxCacheSize()) {
            stringRedisTemplate.opsForList().trim(VERIFICATION_CODE_REDIS_KEY, 0, gmailProperties.getMaxCacheSize() - 1);
        }

        // 维护已处理消息ID集合大小
        Long processedSize = stringRedisTemplate.opsForSet().size(PROCESSED_MESSAGES_KEY);
        if (processedSize != null && processedSize > gmailProperties.getMaxCacheSize()) {
            // 获取所有消息ID
            Set<String> allMessages = stringRedisTemplate.opsForSet().members(PROCESSED_MESSAGES_KEY);
            if (allMessages != null) {
                List<String> messagesList = new ArrayList<>(allMessages);
                // 按照消息ID排序（假设消息ID是递增的）
                messagesList.sort(String::compareTo);
                // 删除最旧的消息ID
                int toRemove = messagesList.size() - gmailProperties.getMaxCacheSize();
                for (int i = 0; i < toRemove; i++) {
                    stringRedisTemplate.opsForSet().remove(PROCESSED_MESSAGES_KEY, messagesList.get(i));
                }
            }
        }
    }

    public void checkNewEmails() throws SocketTimeoutException, SocketException {
        Properties props = new Properties();
        props.put("mail.store.protocol", "imap");
        props.put("mail.imap.host", HOST);
        props.put("mail.imap.port", "993");
        props.put("mail.imap.ssl.enable", "true");
        props.put("mail.imap.ssl.trust", "*");

        // 优化连接设置
        props.put("mail.imap.connectiontimeout", "20000");
        props.put("mail.imap.timeout", "15000");
        //每个线程可以获得独立的IMAP连接，避免线程间竞争同一个连接
        props.put("mail.imap.connectionpoolsize", "15");
        props.put("mail.imap.connectionpooltimeout", "600000");
        
        Store store = null;
        Folder inbox = null;
        try {
            Session session = Session.getInstance(props);
            store = session.getStore("imap");
            store.connect(HOST, gmailProperties.getUsername(), gmailProperties.getAppPassword());

            inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_WRITE);

            // 优化：构建复合搜索条件 - 发件人 + 今天的邮件
            SearchTerm fromTerm = new FromStringTerm(gmailProperties.getTargetSender());
            
            // 添加日期过滤
            Date startDay = Date.from(LocalDate.now().minusDays(2).atStartOfDay(ZoneId.systemDefault()).toInstant());
            SearchTerm dateTerm = new ReceivedDateTerm(ComparisonTerm.GE, startDay);
            
            // 组合搜索条件
            SearchTerm combinedTerm = new AndTerm(fromTerm, dateTerm);
            //只是获取到了邮件的元数据和引用，要拿到主题和正文，需要再从服务器下载
            Message[] messages = inbox.search(combinedTerm);
            log.info("Found {} related messages", messages.length);

            // 进一步限制：如果今天的邮件超过n条，只处理最近的n条
            int maxMessages = Math.min(30, messages.length);
            int startIndex = Math.max(0, messages.length - maxMessages);
            
            log.info("Processing {} messages from index {} to {}", maxMessages, startIndex, messages.length - 1);

            // 批量检查已处理的消息
            List<String> messageIds = new ArrayList<>();
            for (int i = startIndex; i < messages.length; i++) {
                messageIds.add(messages[i].getMessageNumber() + "");
            }
            
            // 批量检查Redis中已处理的消息
            Set<String> processedIds = new HashSet<>();
            for (String messageId : messageIds) {
                Boolean isProcessed = stringRedisTemplate.opsForSet().isMember(PROCESSED_MESSAGES_KEY, messageId);
                if (Boolean.TRUE.equals(isProcessed)) {
                    processedIds.add(messageId);
                }
            }
            
            // 并发处理邮件
            List<CompletableFuture<ValidateCodeResp>> futures = new ArrayList<>();
            List<String> newProcessedIds = new ArrayList<>();
            
            ThreadPoolExecutor executor = ThreadPoolHelper.getGmailProcessorInstance();
            
            for (int i = startIndex; i < messages.length; i++) {
                Message message = messages[i];
                String messageId = message.getMessageNumber() + "";
                
                if (processedIds.contains(messageId)) {
                    log.info("Message {} has already been processed, skipping...", messageId);
                    continue;
                }
                
                // 异步处理每封邮件
                CompletableFuture<ValidateCodeResp> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        return processMessage(message, messageId);
                    } catch (Exception e) {
                        log.error("Error processing message {}: {}", messageId, e.getMessage());
                        return null;
                    }
                }, executor);
                
                futures.add(future);
                newProcessedIds.add(messageId);
            }
            
            // 批量处理结果
            List<ValidateCodeResp> validCodes = new ArrayList<>();
            for (CompletableFuture<ValidateCodeResp> future : futures) {
                try {
                    ValidateCodeResp result = future.get(30, TimeUnit.SECONDS);
                    if (result != null) {
                        validCodes.add(result);
                    }
                } catch (Exception e) {
                    log.error("Error getting future result: {}", e.getMessage());
                }
            }
            
            // 批量存储到Redis
            if (!validCodes.isEmpty()) {
                batchStoreToRedis(validCodes, newProcessedIds);
            }
            
            // 只在最后维护一次缓存
            maintainCacheSize();
            
        } catch (Exception e) {
            log.error("Unexpected error: {}", e.getMessage(), e);
            throw new RuntimeException("调用失败，未知错误", e);
        } finally {
            if (inbox != null) {
                try {
                    inbox.close(false);
                } catch (MessagingException e) {
                    log.warn("Failed to close inbox folder: {}", e.getMessage(), e);
                }
            }

            if (store != null) {
                try {
                    store.close();
                } catch (MessagingException e) {
                    log.warn("Failed to close store: {}", e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 处理单个邮件消息
     */
    private ValidateCodeResp processMessage(Message message, String messageId) throws Exception {
        if(!isVerificationEmail(message)) {
            log.info("Email subject does not contain verification code text, skipping...");
            return null;
        }
        String emailContent = getEmailContent(message);
        
        if (!emailContent.contains("一次性验证码")) {
            log.info("Email content does not contain verification code text, skipping...");
            return null;
        }
        
        String verificationCode = extractVerificationCode(emailContent);
        String cardLastFour = extractCardLastFour(emailContent);
        
        ValidateCodeResp codeRecord = new ValidateCodeResp();
        codeRecord.setMessageId(messageId);
        codeRecord.setCardLastFour(cardLastFour);
        codeRecord.setVerificationCode(verificationCode);
        codeRecord.setReceiveTime(DateUtil.formatDateTime(message.getReceivedDate()));
        codeRecord.setEmailContent(emailContent);
        
        // 标记邮件为已读
        //message.setFlag(Flags.Flag.SEEN, true);
        log.info("New verification code processed: {} for card {}", verificationCode, cardLastFour);
        
        return codeRecord;
    }

    /**
     * 批量存储到Redis
     */
    private void batchStoreToRedis(List<ValidateCodeResp> validCodes, List<String> processedIds) {
        try {
            // 批量存储验证码
            List<String> jsonValues = validCodes.stream()
                    .map(code -> {
                        try {
                            return objectMapper.writeValueAsString(code);
                        } catch (Exception e) {
                            log.error("Error serializing code: {}", e.getMessage());
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            if (!jsonValues.isEmpty()) {
                stringRedisTemplate.opsForList().leftPushAll(VERIFICATION_CODE_REDIS_KEY, jsonValues);
            }
            
            // 批量添加已处理的消息ID
            if (!processedIds.isEmpty()) {
                stringRedisTemplate.opsForSet().add(PROCESSED_MESSAGES_KEY, processedIds.toArray(new String[0]));
            }
            
        } catch (Exception e) {
            log.error("Error batch storing to Redis: {}", e.getMessage(), e);
        }
    }

    private String getEmailContent(Message message) throws MessagingException, IOException {
        if (message instanceof MimeMessage) {
            Object content = message.getContent();
            if (content instanceof String) {
                return (String) content;
            } else if (content instanceof Multipart) {
                Multipart multipart = (Multipart) content;
                StringBuilder result = new StringBuilder();
                for (int i = 0; i < multipart.getCount(); i++) {
                    BodyPart bodyPart = multipart.getBodyPart(i);
                    if (bodyPart.getContentType().toLowerCase().contains("text/plain")) {
                        result.append(bodyPart.getContent().toString());
                    }
                }
                return result.toString();
            }
        }
        return "";
    }

    private String extractVerificationCode(String emailContent) {
        Matcher matcher = VERIFICATION_CODE_PATTERN.matcher(emailContent);
        if (matcher.find()) {
            return matcher.group();
        }
        return "";
    }

    private String extractCardLastFour(String emailContent) {
        Matcher matcher = CARD_LAST_FOUR_PATTERN.matcher(emailContent);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }

    /**
     * 获取最近的验证码记录列表
     * @return 最近的验证码记录，按时间倒序排序
     */
    public List<ValidateCodeResp> getRecentVerificationCodes() {
        Long size = stringRedisTemplate.opsForList().size(VERIFICATION_CODE_REDIS_KEY);
        if (size == null || size == 0) {
            return new ArrayList<>();
        }
        
        List<String> jsonCodes = stringRedisTemplate.opsForList()
                .range(VERIFICATION_CODE_REDIS_KEY, 0, -1);
        
        List<ValidateCodeResp> codes = new ArrayList<>();
        if (jsonCodes != null) {
            for (String jsonCode : jsonCodes) {
                try {
                    ValidateCodeResp code = objectMapper.readValue(jsonCode, ValidateCodeResp.class);
                    codes.add(code);
                } catch (Exception e) {
                    log.error("Error parsing JSON code: {}", jsonCode, e);
                }
            }
            codes.sort((a, b) -> b.getReceiveTime().compareTo(a.getReceiveTime()));
        }
        
        return codes;
    }

    /**
     * 根据卡片尾号查询验证码记录
     * @param cardLastFour 卡片后四位
     * @return 匹配的验证码记录，按接收时间倒序排序
     */
    public List<ValidateCodeResp> getVerificationCodesByCardLastFour(String cardLastFour) {
        if (StrUtil.isBlank(cardLastFour)) {
            return new ArrayList<>();
        }

        List<ValidateCodeResp> allCodes = getRecentVerificationCodes();
        return allCodes.stream()
                .filter(code -> cardLastFour.equals(code.getCardLastFour()))
                .sorted((a, b) -> b.getReceiveTime().compareTo(a.getReceiveTime()))
                .collect(Collectors.toList());
    }

    /**
     * 根据卡片尾号查询最新的验证码记录
     * @param cardLastFour 卡片后四位
     * @return 最新的验证码记录，如果没有找到则返回null
     */
    public ValidateCodeResp getLatestVerificationCodeByCardLastFour(String cardLastFour) {
        if (StrUtil.isBlank(cardLastFour)) {
            return null;
        }

        List<ValidateCodeResp> codes = getVerificationCodesByCardLastFour(cardLastFour);
        return codes.isEmpty() ? null : codes.get(0);
    }

    private boolean isVerificationEmail(Message message) throws MessagingException {
        String subject = message.getSubject();
        //log.info("Email subject is {}", subject);
        return subject != null && subject.contains("一次性验证码");
    }

    private void batchMarkAsRead(List<Message> messages) throws MessagingException {
        for (Message message : messages) {
            message.setFlag(Flags.Flag.SEEN, true);
        }
    }
}
