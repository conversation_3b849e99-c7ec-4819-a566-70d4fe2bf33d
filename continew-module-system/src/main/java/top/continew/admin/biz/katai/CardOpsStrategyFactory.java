/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.katai;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.starter.core.validation.CheckUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Component
public class CardOpsStrategyFactory {

    private Map<CardPlatformEnum, CardOpsStrategy> strategies;

    @Autowired
    public CardOpsStrategyFactory(Set<CardOpsStrategy> strategySet) {
        createStrategy(strategySet);
    }

    public CardOpsStrategy findStrategyIgnoreNull(int platform) {
        CardPlatformEnum fansCountPlatformEnum = null;
        for (CardPlatformEnum platformEnum : CardPlatformEnum.values()) {
            if (platformEnum.getValue() == platform) {
                fansCountPlatformEnum = platformEnum;
                break;
            }
        }
        if (fansCountPlatformEnum == null) {
            return null;
        }
        return strategies.get(fansCountPlatformEnum);
    }

    public CardOpsStrategy findStrategyIgnoreNull(CardPlatformEnum platform) {
        return strategies.get(platform);
    }

    public CardOpsStrategy findStrategy(CardPlatformEnum platform) {
        CardOpsStrategy result = strategies.get(platform);
        CheckUtils.throwIfNull(result, "该平台暂不支持API操作");
        return result;
    }

    private void createStrategy(Set<CardOpsStrategy> strategySet) {
        strategies = new HashMap<>();
        strategySet.forEach(strategy -> strategies.put(strategy.getCardPlatform(), strategy));
    }
}
