/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.katai.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.core.utils.DateUtil;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardStatusEnum;
import top.continew.admin.biz.enums.CardTransactionStatusEnum;
import top.continew.admin.biz.enums.CardTransactionTypeEnum;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.katai.AmzClient;
import top.continew.katai.amz.AmzConfig;
import top.continew.katai.amz.model.req.*;
import top.continew.katai.amz.model.resp.*;
import top.continew.katai.exception.ThirdException;
import top.continew.katai.utils.Common;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * AMZ卡台策略实现类
 *
 * <AUTHOR>
 * @since 2025/1/1
 */
@Slf4j
@Service
public class AmzOpsStrategyImpl implements CardOpsStrategy {

    public static final int DEFAULT_PAGE_SIZE = 100;
    private volatile AmzClient client;
    private final Object lock = new Object();
    public static final String APP_ID = "2025840271668471";
    private static final String CARD_BIN = "4866993";

    @PostConstruct
    public void init() {
        prodInit();
    }

    /**
     * 生产的配置
     */
    private void prodInit() {
        AmzConfig config = new AmzConfig();
        config.setAppId(APP_ID);
        config.setAppKey("510f473c8afba0679f2cc3fd654b1476fa99a2c6");
        config.setPrivateKey("MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCv4PZioynDI1xt" + "lRQX3Sx89HCombMmVwAHmBOYWcWj1231uZfqCLyz5LpKk/cyCjQXXg7iTVl6e2Ma" + "atytSsYCFbOrxpR4PdcCgFlX1j6a06YQBmjQLZR6ASx7xMeYqfQnxrLxh4lXM3Gf" + "h3Ps5E/gjwZUBz0Lu6kb5U9H/BPyxzZLtaN2VxaE1iXVQFzrOS38aFkp2+P0jc6P" + "nMrY5g66MM1NRNGAapseq7dvu2/Lw3kEEQR8VRwOcQ9tIJX2996bfodNUs65Ayke" + "+Vnn4ongFM5nDhTbrehiSpl98ZIngbq1LrDDIIFlLRSAIfVTEzFc/ybCnUfndNaV" + "HaYYrGNVAgMBAAECggEAOwj56tX1uKhv5+B4ZCI7Qp4SCqW8uovNWL81JJhNfNdc" + "83Qeh9Hy9Tc7SLkt6j/+iDFsMY3hEPnFpCJmfExy9lQugOQhBdNMDmzikFc7oGCW" + "hx5/pBEIm4M1WT4N+TJi388UVXo3IWwFIZUn1kY0gZ3AdAgR95RACTsJR5JXZ8/j" + "57fqLMRN0aXKgUun3BCUxiSgDBNhnqYIorHal0ZmFVxt8D8dD0XMBleIQzszG1Rn" + "hnHF8qGoPyGtJuXByyBJjSDG1UWYtLoiVEYpl90EG4Ky2qzDzjGYoGEThQUBEggS" + "8f0W+a6pxVdMENdv9lizeTRsfSZbTIhKMpvXrdnaQQKBgQDaxfJ+Ro6OD4NVwe5G" + "5J95QaLkSDhulO1qSkFXgfMf44baNeAHC3zpmcVu8pEe6/Edvs+SDcw2giJorOR9" + "lPuNPfrpY+rGVPqFCPjde3KFwWKRoX0doN5pXlthUuUa4fF/qbFhUfazQnJtkSad" + "naYrLJ3Iq+ibQz8mWYbjfPMjTQKBgQDNznmZFmKJLi1EyUIbJkW4d+jg8DcYKE5n" + "GhtcJQ84KDaDtqDBQjnVaEbR9uwZK+O7nlYuKXWpU64w+TlpHOeqSDDIOY9YIGka" + "VzZYdrHWctNOg7PxNrKmKfgQpXfg1ZkcOm83wzYZx2LdxHbmPxDJvszgql/y3+Ql" + "7FZs2BSsKQKBgFk48bTeQbVeTknjVbJD+2YQhsZjTeCLjGgU8KEntmgC+zRzVHKL" + "FN6QgBzHgLgDscpXz3/ZcLeqSy41lNpCsHTiGjqlLVLFxYYMKrLpbcNvIywRmF1F" + "BnAis1H15MoZ68wNAPKX4u0o5FIbKIyPhv2ErLTyp2LrAg455PFSPDbhAoGAA44J" + "c6d7VDtD9Og1CBsFIUaQ8zGzAevQTt+YkWLifGnVZzkEVdI7BYCjmITXVyf26wq1" + "Gl8E9UPIwnfLkhE5vys4DH1SCIemyRmwip6iO74IAFcuCICVXWFkM4VoKK4H0wD/" + "YsswaAmVn5cBJsD4HUP6bfcDaUKMwkruwzqIQOECgYAHQjxSvKN82oHGfmiXIEhS" + "fO4CA7DXTPbe1O+jLUsEI4fIdldNJsIQy8DpiYg6kY5hrEgfILaP3th1GQHq73pP" + "WiCO1ALR1SqrnOA4Rvx9X1Ojm+PgQEHrScv2k/q4iJyyDGmOnvN6IMbwpCnN/Pw1" + "8KSp7hYIHYopSoFQAyyRAQ==");
        config.setPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr+D2YqMpwyNcbZUUF90s" + "fPRwqJmzJlcAB5gTmFnFo9dt9bmX6gi8s+S6SpP3Mgo0F14O4k1ZentjGmrcrUrG" + "AhWzq8aUeD3XAoBZV9Y+mtOmEAZo0C2UegEse8THmKn0J8ay8YeJVzNxn4dz7ORP" + "4I8GVAc9C7upG+VPR/wT8sc2S7WjdlcWhNYl1UBc6zkt/GhZKdvj9I3Oj5zK2OYO" + "ujDNTUTRgGqbHqu3b7tvy8N5BBEEfFUcDnEPbSCV9vfem36HTVLOuQMpHvlZ5+KJ" + "4BTOZw4U263oYkqZffGSJ4G6tS6wwyCBZS0UgCH1UxMxXP8mwp1H53TWlR2mGKxj" + "VQIDAQAB");
        config.setEndpoint("ymapi.amzkeys.com:15970");

        client = new AmzClient(config);
    }

    @Override
    public CardPlatformEnum getCardPlatform() {
        return CardPlatformEnum.AMZ;
    }

    //TODO
    /*
    1、不能拉取到非正常的卡片
    2、不能拉取到卡片余额流水
    3、没有激活功能
    4、交易的状态没有那么多
    5、提现、作废、充值、开卡，都是走的申请接口
     */

    @Override
    public List<CardDO> getCardList(LocalDateTime start, LocalDateTime end, Integer syncPage) {
        log.info("======【{}】开始获取卡片列表======", getCardPlatform().getDescription());
        List<CardDO> cardList = new ArrayList<>();

        try {
            int page = 1;
            int totalPages = 10; // 初始设置为10页，后续根据实际数据总数计算
            final int PAGE_SIZE = 20;

            while (page <= totalPages) {
                log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);

                AmzGetCardRequest request = AmzGetCardRequest.builder().pageNo(page).pageSize(PAGE_SIZE).build();

                AmzGetCardResponse response = getClient().getCards(request);

                if (response != null) {
                    // 计算总页数，假设response中有total字段
                    totalPages = (int)Math.ceil((double)response.getTotalSize() / PAGE_SIZE);

                    for (AmzCardResponse item : response.getCards()) {
                        CardDO cardDO = new CardDO();
                        cardDO.setCardNumber(item.getCardNo());
                        cardDO.setPlatform(getCardPlatform());
                        cardDO.setPlatformCardId(StrUtil.toString(item.getRequestId()));
                        cardDO.setStatus(CardStatusEnum.NORMAL);
                        cardDO.setBalance(item.getTotalPrice());
                        cardDO.setAssociation(null);
                        cardDO.setUsedAmount(null);
                        //返回的是2028-07这种格式，需要转为07/28这一种格式
                        if (null != item.getValidDate() && item.getValidDate().contains("-") && item.getValidDate()
                            .length() == 7) {
                            cardDO.setExpireDate(item.getValidDate().substring(5, 7) + "/" + item.getValidDate()
                                .substring(2, 4));
                        } else {
                            cardDO.setExpireDate(item.getValidDate());
                        }
                        cardDO.setCvv(StrUtil.toString(item.getCvv()));
                        cardDO.setStatus(CardStatusEnum.NORMAL);
                        cardDO.setOpenTime(DateUtil.parseDateTime(item.getCreateTime()));
                        cardDO.setPlatformAdId(null);
                        cardList.add(cardDO);
                    }

                    // 如果指定了同步页数且已达到指定页数，则退出循环
                    if (syncPage != null && page == syncPage) {
                        break;
                    }

                    // 如果当前页的数据少于页大小，说明已经是最后一页
                    if (response.getCards().size() < PAGE_SIZE) {
                        break;
                    }

                    page++;
                } else {
                    // 如果没有数据，直接退出循环
                    break;
                }
            }

            log.info("======【{}】获取卡片列表完成，共{}张卡片======", getCardPlatform().getDescription(), cardList.size());
            return cardList;

        } catch (Exception e) {
            log.error("【{}】获取卡片列表失败：{}", getCardPlatform().getDescription(), e.getMessage(), e);
            return cardList;
        }
    }

    @Override
    public List<CardBalanceDO> getCardBalanceList(LocalDateTime start, LocalDateTime end) {
        return List.of();
    }

    @Override
    public List<CardTransactionDO> getCardTransactionList(LocalDateTime start, LocalDateTime end, String status) {
        log.info("======【{}】开始同步卡片交易数据======", getCardPlatform().getDescription());

        if (start == null) {
            start = LocalDateTimeUtil.parse("2025-07-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        }
        if (end == null) {
            end = LocalDateTimeUtil.now();
        }

        //FIXME
        //start = start.minusHours(8);
        //end = end.minusHours(8);

        int page = 1;
        int totalPages = 10;

        List<CardTransactionDO> list = new ArrayList<>();

        while (page <= totalPages) {
            log.info("【{}】开始同步第{}页数据...", getCardPlatform().getDescription(), page);
            try {
                AmzGetTransactionRequest request = AmzGetTransactionRequest.builder()
                    .startDate(LocalDateTimeUtil.format(start, "yyyy-MM-dd HH:mm:ss"))
                    .endDate(LocalDateTimeUtil.format(end, "yyyy-MM-dd HH:mm:ss"))
                    .pageNo(page)
                    .pageSize(100)
                    .build();

                AmzGetTransactionResponse result = getClient().getTransactions(request);
                if (result != null) {
                    totalPages = (int)Math.ceil((double)result.getTotalSize() / DEFAULT_PAGE_SIZE);
                    System.out.println(JSON.toJSONString(result.getItem()));
                    if (CollUtil.isNotEmpty(result.getItem())) {
                        list.addAll(this.convertCardTransactionList(result.getItem()));
                    }
                }

                page++;

            } catch (ThirdException e) {
                log.info("【{}】交易数据同步错误：code={}, message={}", getCardPlatform().getDescription(), e.getCode(), e.getMessage());
                break;
            }
        }

        log.info("======【{}】卡片交易数据完成======", getCardPlatform().getDescription());
        return list;
    }

    @Override
    public void rechargeCard(CardDO cardDO, BigDecimal amount) {
        log.info("======【{}】开始充值卡片{}，金额：{}======", getCardPlatform().getDescription(), cardDO.getPlatformCardId(), amount);

        AmzRechargeRequest request = AmzRechargeRequest.builder()
            .cardType(Integer.valueOf(CARD_BIN))
            .requestId(Long.valueOf(cardDO.getPlatformCardId()))
            .amount(amount)
            .accountType("USD")
            .build();

        try {
            AmzTaskResponse response = getClient().recharge(request);
            if (response != null) {
                log.info("======【{}】卡片{}充值任务提交成功，任务ID：{}======", getCardPlatform().getDescription(), cardDO.getPlatformCardId(), response.getTaskId());
            } else {
                throw new BusinessException("充值失败");
            }
        } catch (ThirdException e) {
            log.error("【{}】卡片{}充值失败：{}", getCardPlatform().getDescription(), cardDO.getPlatformCardId(), e.getMessage());
            throw e;
        }
    }

    @Override
    public void activeCard(CardDO cardDO) {
        throw new BusinessException("不支持卡片激活操作");
    }

    @Override
    public void inactiveCard(CardDO cardDO) {
        log.info("======【{}】开始冻结卡片：{}", getCardPlatform().getDescription(), cardDO.getPlatformCardId());

        try {
            AmzInvalidRequest request = AmzInvalidRequest.builder()
                .requestId(Long.valueOf(cardDO.getPlatformCardId()))
                .build();

            AmzInvalidResponse response = getClient().invalid(request);
            if (response != null) {
                log.info("======【{}】卡片{}冻结完成======", getCardPlatform().getDescription(), cardDO.getPlatformCardId());
            } else {
                throw new BusinessException("冻结失败");
            }
        } catch (ThirdException e) {
            log.error("【{}】卡片{}冻结失败：{}", getCardPlatform().getDescription(), cardDO.getPlatformCardId(), e.getMessage());
            throw e;
        }
    }

    @Override
    public BigDecimal withdrawCard(CardDO cardDO, BigDecimal amount) {
        String cardNumber = cardDO.getCardNumber();
        log.info("======【{}】开始提现卡片{}======", getCardPlatform().getDescription(), cardNumber);

        BigDecimal withdrawAmount = amount != null ? amount : BigDecimal.ZERO;

        try {
            AmzTransferBalanceRequest request = AmzTransferBalanceRequest.builder()
                .transferAmount(withdrawAmount)
                .requestId(Long.valueOf(cardDO.getPlatformCardId()))
                .build();

            AmzTaskResponse response = getClient().transferBalance(request);
            if (response != null) {
                log.info("======【{}】卡片{}提现任务提交成功，任务ID：{}======", getCardPlatform().getDescription(), cardNumber, response.getTaskId());
            } else {
                throw new BusinessException("提现失败");
            }
        } catch (ThirdException e) {
            log.error("【{}】卡片{}提现失败：{}", getCardPlatform().getDescription(), cardNumber, e.getMessage());
            throw e;
        }

        return withdrawAmount;
    }

    @Override
    public CardDO openCard(JSONObject data) {
        AmzCreateCardRequest request = AmzCreateCardRequest.builder()
            .accountType("USD")
            .amount(BigDecimal.TEN)
            .cardType(data.getIntValue("cardBin"))
            .number(1)
            .build();

        try {
            AmzTaskResponse response = getClient().createCard(request);
            log.info("AMZ开卡创建结果：{}", response);

            if (response != null) {
                // 开卡任务提交成功，需要通过任务ID查询结果
                log.info("AMZ开卡任务提交成功，任务ID：{}", response.getTaskId());
                // 等待一段时间后查询结果
                Common.sleep(3000);
                return getApplyCardResult(StrUtil.toString(response.getTaskId()));
            } else {
                throw new BusinessException("开卡失败");
            }
        } catch (ThirdException e) {
            log.error("AMZ开卡失败：{}", e.getMessage());
            throw new BusinessException("开卡失败：" + e.getMessage());
        }
    }

    @Override
    public CardDO getApplyCardResult(String requestId) {
        try {
            AmzGetTaskDetailRequest request = AmzGetTaskDetailRequest.builder()
                .taskType(1)
                .taskId(Long.valueOf(requestId))
                .build();

            AmzTaskResponse<AmzCardResponse> resultResp = getClient().getTaskDetail(request, AmzCardResponse.class);
            log.info("AMZ开卡结果：{}", resultResp);

            //任务状态：0未处理，1已完成，2处理中，3处理异常，4部分成功，5全部失败
            if (resultResp != null && null != resultResp.getTaskStatus()) {
                if (resultResp.getTaskStatus().equals(1)) {
                    AmzCardResponse cardData = resultResp.getData();
                    if (cardData != null) {
                        CardDO cardDO = new CardDO();
                        cardDO.setCardNumber(cardData.getCardNo());
                        cardDO.setPlatform(getCardPlatform());
                        cardDO.setAssociation(StrUtil.EMPTY);
                        cardDO.setBalance(cardData.getOpenCardAmount());
                        //返回的是2028-07这种格式，需要转为07/28这一种格式
                        if (null != cardData.getValidDate() && cardData.getValidDate()
                            .contains("-") && cardData.getValidDate().length() == 7) {
                            cardDO.setExpireDate(cardData.getValidDate().substring(5, 7) + "/" + cardData.getValidDate()
                                .substring(2, 4));
                        } else {
                            cardDO.setExpireDate(cardData.getValidDate());
                        }
                        cardDO.setCvv(StrUtil.toString(cardData.getCvv()));
                        cardDO.setStatus(CardStatusEnum.NORMAL);
                        cardDO.setOpenTime(DateUtil.parseDateTime(cardData.getCreateTime()));
                        cardDO.setPlatformCardId(StrUtil.toString(cardData.getRequestId()));
                        return cardDO;
                    }
                } else if (resultResp.getTaskStatus().equals(3) || resultResp.getTaskStatus().equals(5)) {
                    throw new BusinessException("开卡失败，返回开卡任务状态：" + resultResp.getTaskStatus());
                } else {
                    // 任务还在处理中
                    CardDO cardDO = new CardDO();
                    cardDO.setCardNumber(requestId);
                    cardDO.setPlatform(getCardPlatform());
                    cardDO.setAssociation(StrUtil.EMPTY);
                    cardDO.setBalance(BigDecimal.ZERO);
                    cardDO.setExpireDate(StrUtil.EMPTY);
                    cardDO.setCvv(StrUtil.EMPTY);
                    cardDO.setStatus(CardStatusEnum.PENDING);
                    cardDO.setPlatformCardId(requestId);
                    return cardDO;
                }
            }
        } catch (ThirdException e) {
            log.error("获取AMZ开卡结果失败：{}", e.getMessage());
            throw new BusinessException("获取开卡结果失败：" + e.getMessage());
        }

        return null;
    }

    @Override
    public String getVerifyCode(CardDO card) {
        try {
            log.info("======【{}】开始获取卡片验证码{}======", getCardPlatform().getDescription(), card.getCardNumber());

            AmzAuthCodeResponse result = getClient().getAuthCode();
            if (result != null && CollUtil.isNotEmpty(result.getItem())) {
                // 查找匹配的验证码
                for (AmzAuthCodeItem item : result.getItem()) {
                    if (item != null && StrUtil.isNotBlank(item.getCardNoLast4()) && card.getCardNumber()
                        .substring(card.getCardNumber().length() - 4)
                        .equals(item.getCardNoLast4())) {
                        String verifyCode = String.format("【%s】%s", item.getCreateTime(), item.getAuthCode());
                        log.info("======【{}】{}验证码获取结果：{}======", getCardPlatform().getDescription(), card.getCardNumber(), verifyCode);
                        return verifyCode;
                    }
                }
            }
        } catch (Exception e) {
            log.error("【{}】获取卡片{}验证码失败：{}", getCardPlatform().getDescription(), card.getCardNumber(), e.getMessage());
        }

        log.info("======【{}】{}验证码获取结果：无======", getCardPlatform().getDescription(), card.getCardNumber());
        return "";
    }

    @Override
    public void updateRemark(CardDO card) {
        log.info("======【{}】开始更新卡片{}，备注：{}======", getCardPlatform().getDescription(), card.getCardNumber(), card.getRemark());
        // TODO AMZ平台暂不支持更新备注
        log.info("======【{}】{}备注更新成功======", getCardPlatform().getDescription(), card.getCardNumber());
    }

    @Override
    public List<LabelValueResp<String>> getCardBinList() {
        try {
            //List<AmzCardTypeResponse> cardTypes = getClient().getCardTypes();
            List<LabelValueResp<String>> list = new ArrayList<>();
            LabelValueResp<String> labelValue = new LabelValueResp<>(CARD_BIN, CARD_BIN);
            list.add(labelValue);
            return list;
        } catch (Exception e) {
            log.error("获取AMZ卡片类型失败：{}", e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<CardTransactionDO> convertCardTransactionList(JSONArray jsonArray) {
        List<AmzTransactionItem> list = jsonArray.toJavaList(AmzTransactionItem.class);
        return this.convertCardTransactionList(list);
    }

    @Override
    public BigDecimal getCurrentBalance() {
        return null;
    }

    @Override
    public BigDecimal getCardBalance(CardDO cardDO) {
        try {
            log.info("======【{}】开始获取卡片{}余额======", getCardPlatform().getDescription(), cardDO.getCardNumber());

            // 通过获取卡片详情来获取余额信息
            AmzCardBalanceResponse cardBalance = getClient().getCardBalance(cardDO.getPlatformCardId());
            if (cardBalance != null) {
                log.info("======【{}】卡片{}余额获取成功：{}======", getCardPlatform().getDescription(), cardDO.getCardNumber(), cardBalance.getCardBalance());
                return cardBalance.getCardBalance();
            }

            log.warn("======【{}】卡片{}余额获取失败，返回0======", getCardPlatform().getDescription(), cardDO.getCardNumber());
            return null;

        } catch (Exception e) {
            log.error("【{}】获取卡片{}余额失败：{}", getCardPlatform().getDescription(), cardDO.getCardNumber(), e.getMessage(), e);
            return null;
        }
    }

    @Override
    public CardDO getCardDetail(String cardId) {
        return null;
    }

    @Override
    public CardDO getCardSensitiveDetail(String cardId) {
        return null;
    }

    private List<CardTransactionDO> convertCardTransactionList(List<AmzTransactionItem> list) {
        List<CardTransactionDO> resultList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (AmzTransactionItem item : list) {
                if (item.getTransactionType().equals("结算")) {
                    continue;
                }
                CardTransactionDO cardTransactionDO = new CardTransactionDO();
                cardTransactionDO.setPlatform(getCardPlatform());
                cardTransactionDO.setPlatformCardId(item.getRequestId());
                cardTransactionDO.setTransactionId(item.getTransactionId());
                cardTransactionDO.setTransType(getCardTransactionType(item.getTransactionType()));
                cardTransactionDO.setOriginTransType(item.getTransactionType() + "," + item.getControlledResponse());
                cardTransactionDO.setTransStatus(getCardTransactionStatus(item.getControlledResponse()));
                if (cardTransactionDO.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION)) {
                    cardTransactionDO.setTransAmount(item.getTraderAmount().negate());
                } else {
                    cardTransactionDO.setTransAmount(item.getTraderAmount());
                }
                if (cardTransactionDO.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION)) {
                    cardTransactionDO.setTransactionId(cardTransactionDO.getTransactionId() + "-1");
                } else if (cardTransactionDO.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION_BACK)) {
                    cardTransactionDO.setTransactionId(cardTransactionDO.getTransactionId() + "-2");
                } else if (cardTransactionDO.getTransType().equals(CardTransactionTypeEnum.OTHER)) {
                    cardTransactionDO.setTransactionId(cardTransactionDO.getTransactionId() + "-3");
                }
                cardTransactionDO.setTransCurrency(item.getTraderBillingCurrencyCode());
                cardTransactionDO.setTransTime(LocalDateTimeUtil.parse(item.getTransactionDate(), "yyyy-MM-dd HH:mm:ss"));
                cardTransactionDO.setCardNumber(StrUtil.EMPTY);
                cardTransactionDO.setChinaTime(cardTransactionDO.getTransTime());
                cardTransactionDO.setTransDetail(item.getMerchantName());
                cardTransactionDO.setRemark(item.getResponse());
                resultList.add(cardTransactionDO);
            }
        }
        return resultList;
    }

    /**
     * 获取AMZ客户端
     *
     * @return AmzClient实例
     */
    private AmzClient getClient() {
        return client;
    }

    /**
     * 获取交易类型
     */
    private CardTransactionTypeEnum getCardTransactionType(String type) {
        if (StrUtil.isBlank(type)) {
            return CardTransactionTypeEnum.OTHER;
        }

        return switch (type) {
            case "授权" -> CardTransactionTypeEnum.AUTHORIZATION;
            case "冲账" -> CardTransactionTypeEnum.AUTHORIZATION_BACK;
            default -> CardTransactionTypeEnum.OTHER;
        };
    }

    /**
     * 获取交易状态
     */
    private CardTransactionStatusEnum getCardTransactionStatus(String controlledResponse) {
        if (StrUtil.isBlank(controlledResponse)) {
            return CardTransactionStatusEnum.OTHER;
        }
        if ("成功".equals(controlledResponse)) {
            return CardTransactionStatusEnum.SUCCESS;
        } else {
            return CardTransactionStatusEnum.FAIL;
        }
    }
}