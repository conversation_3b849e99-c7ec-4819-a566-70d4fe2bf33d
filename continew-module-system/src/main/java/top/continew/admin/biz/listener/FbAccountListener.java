/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.listener;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.biz.event.FbAccountDisabledEvent;
import top.continew.admin.biz.event.FbAccountUpdateEvent;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.entity.FbAccountDO;
import top.continew.admin.biz.service.AdAccountService;
import top.continew.admin.biz.service.FbAccountService;

import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class FbAccountListener {

    private final FbAccountService fbAccountService;

    private final AdAccountService adAccountService;

    @Async
    @EventListener
    public void update(FbAccountUpdateEvent event) {
        FbAccountDO fbAccountDO = (FbAccountDO)event.getSource();
        if (fbAccountDO.getPlatformAccountId() == null) {
            return;
        }
        if (fbAccountDO.getId() != null) {
            fbAccountService.updateById(fbAccountDO);
        } else {
            if (!StringUtils.isNumeric(fbAccountDO.getPlatformAccountId())) {
                return;
            }
            fbAccountService.update(fbAccountDO, new LambdaUpdateWrapper<FbAccountDO>()
                .eq(FbAccountDO::getPlatformAccountId, fbAccountDO.getPlatformAccountId()));
        }
    }

    @Async
    @EventListener
    public void disabled(FbAccountDisabledEvent event) {
        String fbId = (String)event.getSource();
        if (StringUtils.isBlank(fbId)) {
            return;
        }
        adAccountService.update(new LambdaUpdateWrapper<AdAccountDO>().eq(AdAccountDO::getPlatformAccountId, fbId)
            .set(AdAccountDO::getAccountStatus, AdAccountStatusEnum.BANNED).set(AdAccountDO::getBanTime, LocalDateTime.now()));
    }

}
