/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.resp.AdAccountDailyStatReportResp;
import top.continew.admin.biz.model.resp.AdAccountResp;
import top.continew.admin.biz.model.resp.AdAccountStatResp;
import top.continew.admin.common.constant.CacheConstants;
import top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp;
import top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.AdAccountInsightDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 广告户每日消耗 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/14 14:43
 */
public interface AdAccountInsightMapper extends BaseMapper<AdAccountInsightDO> {


    /**
     * 统计指定日期范围内的广告户消耗金额
     *
     * @param platformAdId 广告户ID
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @return 消耗总金额
     */
    BigDecimal sumSpendByDateRange(@Param("platformAdId") String platformAdId,
                                   @Param("startDate") String startDate,
                                   @Param("endDate") String endDate);


    /**
     * 查询仪表盘 消耗 总览
     *
     * @return 仪表盘 消耗 总览
     */
    DashboardOverviewCommonResp selectDashboardOverviewSpent();


    @Cached(key = "#months[0]", name = CacheConstants.DASHBOARD_KEY_PREFIX + "SPENT:")
    List<DashboardChartCommonResp> selectListDashboardAnalysisSpent(@Param("months") List<String> months);
}