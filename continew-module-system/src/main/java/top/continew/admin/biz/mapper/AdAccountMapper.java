/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.biz.model.entity.AdAccountDO;
import top.continew.admin.biz.model.query.AdAccountQuery;
import top.continew.admin.biz.model.query.InactiveAccountAnalyzeQuery;
import top.continew.admin.biz.model.resp.AdAccountBrowserResp;
import top.continew.admin.biz.model.resp.AdAccountInventoryResp;
import top.continew.admin.biz.model.resp.AdAccountKeepStatusStatResp;
import top.continew.admin.biz.model.resp.AdAccountResp;
import top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp;
import top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 广告账号 Mapper
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
public interface AdAccountMapper extends BaseMapper<AdAccountDO> {

    IPage<AdAccountResp> selectCustomPage(IPage<AdAccountResp> page,
                                          @Param(Constants.WRAPPER) QueryWrapper<AdAccountDO> queryWrapper);

    List<AdAccountResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<AdAccountDO> queryWrapper);

    List<AdAccountInventoryResp> getAdAccountInventory(@Param("before22hourFilter") boolean before22hourFilter);

    List<AdAccountInventoryResp> getExternalAccountInventory();

    List<AdAccountInventoryResp> getAdAccountKeepSuccessInventory();

    /**
     * 更新浏览器编号
     *
     * @param platformAdId
     * @param browserNo
     */
    void updateBrowserNo(@Param("platformAdId") String platformAdId, @Param("browserNo") String browserNo);

    /**
     * 查询仪表盘 库存 总览
     *
     * @return 仪表盘 库存 总览
     */
    DashboardOverviewCommonResp selectDashboardOverviewInventory();

    List<DashboardChartCommonResp> selectListDashboardAnalysisInventory();

    IPage<AdAccountResp> selectInactiveAccounts(Page<AdAccountResp> page,
                                                @Param("query") InactiveAccountAnalyzeQuery query);

    Long selectCountInactiveAccounts(@Param("query") InactiveAccountAnalyzeQuery query);

    /**
     * 获取拒付金额
     *
     * @return
     */
    @Select("SELECT IFNULL(sum(balance), 0) FROM biz_ad_account where (sale_status = 2 and account_status = 2) or sale_status = 5")
    BigDecimal getRefuseAmount();

    /**
     * 获取总库存
     *
     * @return
     */
    Integer getTotalInventory(@Param("saleStatuses") Integer[] saleStatuses);

    /**
     * 查询养号状态统计
     *
     * @param query 查询条件
     * @return 统计结果
     */
    List<AdAccountKeepStatusStatResp> selectKeepStatusStat(@Param("query") AdAccountQuery query);

    /**
     * 查询库存账号
     *
     * @return
     */
    List<AdAccountResp> selectInventoryList();

    List<AdAccountDO> selectWaitSaleAdAccountList(@Param("timeZone") String timeZone,
                                                  @Param("useCleanBm5") Integer useCleanBm5,
                                                  @Param("isLowLimit") Boolean isLowLimit,
                                                  @Param("requireVo") Boolean requireVo,
                                                  @Param("authTimeThreshold") LocalDateTime authTimeThreshold,
                                                  @Param("bmType") Integer bmType);

    void updateRemark(@Param("remark") String remark, @Param("ids") List<Long> ids);

    /**
     * 批量获取广告户浏览器编号
     *
     * @param platformAdIds 广告户ID列表
     * @return 广告户ID和浏览器编号的映射
     */
    List<AdAccountBrowserResp> selectBrowserNoByPlatformAdIds(@Param("platformAdIds") List<String> platformAdIds);

    /**
     * 获取广告户清零后的充值金额
     *
     * @param customerId
     * @param platformAdId
     * @return
     */
    BigDecimal getRechargeAmountAfterClear(@Param("customerId") Long customerId,
                                           @Param("platformAdId") String platformAdId);
}