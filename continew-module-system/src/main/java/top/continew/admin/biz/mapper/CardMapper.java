/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.resp.CardResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.util.List;

/**
 * 卡片 Mapper
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
public interface CardMapper extends BaseMapper<CardDO> {

    /**
     * 获取客户卡片余额（去除自用户）
     *
     * @return
     */
    @Select("SELECT IFNULL(sum(balance), 0) FROM biz_card where platform_ad_id in (select ad_account_id from biz_ad_account_order where customer_id not in (select id from biz_customer where is_self_account = true) and biz_ad_account_order.status = 3)")
    Long getTotalBalance();

    IPage<CardResp> selectCustomPage(IPage<CardDO> page, @Param(Constants.WRAPPER) QueryWrapper<CardDO> queryWrapper);

    List<CardResp> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<CardDO> queryWrapper);

    /**
     * 更新光子易卡片的余额信息
     */
    void updateCardUsedAmount(@Param("platform") Integer platform);

    /**
     * 获取需要提现的卡片
     *
     * @return
     */
    List<CardResp> getNeedWithdrawCard();

    List<String> cardHeadList();

}