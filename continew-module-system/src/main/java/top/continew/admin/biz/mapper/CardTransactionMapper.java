/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.common.constant.CacheConstants;
import top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp;
import top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡片交易流水 Mapper
 *
 * <AUTHOR>
 * @since 2024/12/31 21:57
 */
public interface CardTransactionMapper extends BaseMapper<CardTransactionDO> {

    /**
     * 统计指定时间范围内的卡台交易金额
     *
     * @param platformAdId 广告户ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 交易总金额
     */
    BigDecimal sumAmountByDateRange(@Param("platformAdId") String platformAdId,
                                    @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);

    void updateCardNumber(@Param("platform") Integer platform);

    /**
     * 更新广告户ID数据
     */
    /**
     * 更新广告账户ID（指定时间范围）
     */
    void updateAdAccountIdByTimeRange(@Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 更新客户ID（指定时间范围）
     */
    void updateCustomerIdByTimeRange(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询交易汇总数据
     */
    CardTransactionSummaryResp selectTransactionSummary(@Param("platform") Integer platform,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询卡片汇总数据
     */
    CardTransactionSummaryResp selectCardSummary(@Param("platform") Integer platform,
                                                 @Param("startTime") LocalDateTime startTime,
                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户维度统计
     */
    IPage<CardTransactionStatByCardholderResp> selectStatisticsByCardholderPage(IPage<CardTransactionStatByCardholderResp> page,
                                                                                @Param("platform") Integer platform,
                                                                                @Param("startTime") LocalDateTime startTime,
                                                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 查询日期维度统计
     */
    IPage<CardTransactionStatByDateResp> selectStatisticsByDatePage(IPage<CardTransactionStatByCardholderResp> page,
                                                                    @Param("platform") Integer platform,
                                                                    @Param("startTime") LocalDateTime startTime,
                                                                    @Param("endTime") LocalDateTime endTime);

    List<CardTransactionStatByDateResp> selectStatisticsByDateList(@Param("platform") Integer platform,
                                                                   @Param("startTime") LocalDateTime startTime,
                                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询日期维度统计
     */
    IPage<CardTransactionStatByTimezoneResp> selectStatisticsByTimezonePage(IPage<CardTransactionStatByTimezoneResp> page,
                                                                            @Param("platform") Integer platform,
                                                                            @Param("startTime") LocalDateTime startTime,
                                                                            @Param("endTime") LocalDateTime endTime);

    DashboardOverviewCommonResp selectDashboardOverviewSpent();

    @Cached(key = "#months[0]", name = CacheConstants.DASHBOARD_KEY_PREFIX + "CARD_SPENT:")
    List<DashboardChartCommonResp> selectListDashboardAnalysisSpent(@Param("months") List<String> months);

    @Select("select COALESCE(-SUM(trans_amount), 0) from biz_card_transaction where customer_id = #{customerId} and ad_account_id = #{platformAdId} and trans_status != 4 and stat_time > #{startTime}")
    BigDecimal getAdAccountCardSpent(@Param("customerId") Long customerId,
                                     @Param("platformAdId") String platformAdId,
                                     @Param("startTime") LocalDateTime startTime);

    /**
     * 获取客户广告户每日卡台消耗
     *
     * @param statDate
     * @return
     */
    List<CustomerAdAccountCardSpentResp> getCustomerAdAccountDailySpent(@Param("statDate") LocalDate statDate);

}