/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import com.alicp.jetcache.anno.Cached;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.biz.model.entity.CustomerBalanceRecordDO;
import top.continew.admin.common.constant.CacheConstants;
import top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp;
import top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户余额变更记录 Mapper
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
public interface CustomerBalanceRecordMapper extends BaseMapper<CustomerBalanceRecordDO> {

    /**
     * 查询仪表盘 打款 总览
     *
     * @return 仪表盘 打款 总览
     */
    DashboardOverviewCommonResp selectDashboardOverviewCustomerTransfer();

    /**
     * 查询仪表盘 充值 总览
     *
     * @return 仪表盘 充值 总览
     */
    DashboardOverviewCommonResp selectDashboardOverviewAdAccountRecharge();

    @Cached(key = "#months[0]", name = CacheConstants.DASHBOARD_KEY_PREFIX + "TRANSFER:")
    List<DashboardChartCommonResp> selectListDashboardAnalysisCustomerTransfer(@Param("months") List<String> months);

    @Cached(key = "#months[0]", name = CacheConstants.DASHBOARD_KEY_PREFIX + "RECHARGE:")
    List<DashboardChartCommonResp> selectListDashboardAnalysisAdAccountRecharge(@Param("months") List<String> months);

    /**
     * 打款每日统计
     *
     * @param customerId
     * @param start
     * @param end
     * @return
     */
    List<DashboardChartCommonResp> selectListDashboardAnalysisTransferTimeslot(@Param("customerId") Long customerId,
                                                                               @Param("start") LocalDate start,
                                                                               @Param("end") LocalDate end);

    /**
     * 充值每日统计
     *
     * @param customerId
     * @param platformAdId
     * @param start
     * @param end
     * @return
     */
    List<DashboardChartCommonResp> selectListDashboardAnalysisRechargeTimeslot(@Param("customerId") Long customerId,
                                                                               @Param("platformAdId") String platformAdId,
                                                                               @Param("start") LocalDate start,
                                                                               @Param("end") LocalDate end);

    @Select("select COALESCE(sum(IF(r.type = 3, r.amount, 0)), 0) - COALESCE(sum(IF(r.type in (4, 5), r.amount, 0)), 0) from biz_customer_balance_record r where r.platform_ad_id = #{platformAdId} and r.customer_id = #{customerId} and r.trans_time > #{orderTime}")
    BigDecimal getTotalRechargeAmount(@Param("customerId") Long customerId,
                                      @Param("platformAdId") String platformAdId,
                                      @Param("orderTime") LocalDateTime orderTime);

    @Select("select COALESCE(sum(IF(r.type = 1, r.amount, 0)), 0) - COALESCE(sum(IF(r.type in (8, 9, 10), r.amount, 0)), 0) from biz_customer_balance_record r where r.customer_id = #{customerId}")
    BigDecimal getTotalTransferAmount(@Param("customerId") Long customerId);
}