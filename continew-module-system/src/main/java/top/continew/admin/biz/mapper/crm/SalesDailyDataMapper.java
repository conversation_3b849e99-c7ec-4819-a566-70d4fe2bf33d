package top.continew.admin.biz.mapper.crm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.crm.LeadDO;
import top.continew.admin.biz.model.query.crm.LeadQuery;
import top.continew.admin.biz.model.query.crm.SalesDailyDataQuery;
import top.continew.admin.biz.model.resp.crm.SalesDailyDataResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.crm.SalesDailyDataDO;
import top.continew.starter.extension.datapermission.annotation.DataPermission;

import java.time.LocalDate;
import java.util.List;

/**
* 商务每日数据 Mapper
*
* <AUTHOR>
* @since 2025/07/11 10:55
*/
public interface SalesDailyDataMapper extends BaseMapper<SalesDailyDataDO> {
    /**
     * 分页查询线索列表
     *
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页数据
     */
    @DataPermission(userId = "l.create_user", deptId = "ur.dept_id")
    IPage<SalesDailyDataResp> selectDailyDataPage(IPage<SalesDailyDataDO> page, @Param("query") SalesDailyDataQuery query);

    /**
     * 检查数据库中是否存在重复数据
     * 根据账号类型、客户账号、创建人、记录日期判断重复
     *
     * @param accountType 账号类型
     * @param customerAccount 客户账号
     * @param createUser 创建人ID
     * @return 重复数据列表
     */
    List<SalesDailyDataDO> selectDuplicateData(@Param("accountType") Integer accountType,
                                               @Param("customerAccount") String customerAccount,
                                               @Param("createUser") Long createUser);

}