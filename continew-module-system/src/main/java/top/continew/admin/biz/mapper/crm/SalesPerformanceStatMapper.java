package top.continew.admin.biz.mapper.crm;

import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商务业绩统计 Mapper
 *
 * <AUTHOR>
 * @since 2025/01/16
 */
public interface SalesPerformanceStatMapper extends BaseMapper<Object> {

    /**
     * 统计商务业绩数据
     *
     * @param salesUserIds 商务人员ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 商务业绩统计列表
     */
    List<SalesPerformanceStatResp> selectSalesPerformanceStat(@Param("salesUserIds") List<Long> salesUserIds,
                                                               @Param("startDate") LocalDateTime startDate,
                                                               @Param("endDate") LocalDateTime endDate);
}