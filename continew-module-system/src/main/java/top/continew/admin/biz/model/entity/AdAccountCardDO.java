/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.CardPlatformEnum;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 广告户关联卡实体
 *
 * <AUTHOR>
 * @since 2025/01/02 11:24
 */
@Data
@TableName("biz_ad_account_card")
public class AdAccountCardDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField(fill = FieldFill.INSERT)
    private Long id;

    /**
     * 关联广告户
     */
    private String platformAdId;

    /**
     * 所属平台
     */
    private CardPlatformEnum platform;

    /**
     * 完整卡号
     */
    private String fullCardNumber;

    /**
     * 模糊卡号
     */
    private String fuzzyCardNumber;

    /**
     * 是否默认
     */
    private Boolean isDefault;

    /**
     * 是否已移除
     */
    private Boolean isRemove;

    /**
     * 移除时间
     */
    private LocalDateTime removeTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private String remark;

    /**
     * 过期时间
     */
    private String expireDate;

    /**
     * 卡组织
     */
    private String association;
}