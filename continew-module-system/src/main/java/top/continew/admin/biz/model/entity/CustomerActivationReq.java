package top.continew.admin.biz.model.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NonNull;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class CustomerActivationReq {

    @NotNull(message = "客户ID不能为空")
    private Long id;

    @NotNull(message = "合作时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cooperateTime;

}
