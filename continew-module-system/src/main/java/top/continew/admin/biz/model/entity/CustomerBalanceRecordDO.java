/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.enums.TransactionActionEnum;
import top.continew.starter.extension.crud.model.entity.BaseIdDO;

/**
 * 客户余额变更记录实体
 *
 * <AUTHOR>
 * @since 2024/12/31 09:27
 */
@Data
@TableName("biz_customer_balance_record")
public class CustomerBalanceRecordDO extends BaseIdDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 交易动作
     */
    private TransactionActionEnum action;

    /**
     * 交易类型
     */
    private CustomerBalanceTypeEnum type;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易时间
     */
    private LocalDateTime transTime;

    /**
     * 备注
     */
    private String remark;

    @TableField(fill = FieldFill.INSERT)
    private Long createUser;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 水单凭证
     */
    private String certificate;

    private BigDecimal afterAmount;

    private String platformAdId;

    private String walletTransactionId;
}