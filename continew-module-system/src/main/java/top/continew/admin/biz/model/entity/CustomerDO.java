/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.CustomerSettleTypeEnum;
import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.enums.RechargeFeeHandleMethodEnum;
import top.continew.starter.extension.crud.annotation.DictField;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户实体
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Data
@DictField
@TableName("biz_customer")
public class CustomerDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    private String name;

    /**
     * 关联商务
     */
    private Long businessUserId;

    private Long agentId;

    private String rebateRule;

    /**
     * 手续费百分比
     */
    private BigDecimal feeRatePercent;

    /**
     * 手续费扣款方式
     */
    private RechargeFeeHandleMethodEnum feeHandleMethod;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * TG群ID
     */
    private Long telegramChatId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 机器人权限
     */
    private String robotPermission;

    private BigDecimal buyAccountFee;

    private Boolean isRefund;

    /**
     * 结算方式
     */
    private CustomerSettleTypeEnum settleType;

    /**
     * 结算限额
     */
    private BigDecimal settleLimitAmount;

    /**
     * 预警限额
     */
    private BigDecimal warnLimitAmount;

    /**
     * 上一次结算消耗
     */
    private BigDecimal lastSettleSpent;

    /**
     * 终止时间
     */
    private LocalDateTime terminateTime;

    /**
     * 客户状态
     */
    private CustomerStatusEnum status;

    private Boolean isSelfAccount;

    /**
     * 客户类型：1是正式客户，2是潜在客户
     */
    private CustomerTypeEnum type;

    /**
     * 客户来源ID
     */
    private Long sourceId;

    /**
     * 客户行业
     */
    private Integer industry;

    private String companyName;

    private String customerPosition;

    private String city;

    private String teamSize;

    private String dailyTeamSpending;

    private String productName;

    private String username;

    private String password;

    private LocalDateTime cooperateTime;

    
    public boolean hasRobotPermission(String ops) {
        return robotPermission.contains(ops);
    }
}