/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Facebook 广告组实体
 *
 * <AUTHOR>
 * @since 2025/01/16 10:00
 */
@Data
@TableName("biz_fb_ad_sets")
public class FbAdSetsDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 平台ID（Facebook 返回的 adset_id）
     */
    private String platformId;

    /**
     * 广告组名称
     */
    private String name;

    /**
     * 广告户ID
     */
    private String adAccountId;

    /**
     * 状态：ACTIVE 正常投放 / PAUSED 暂停 / DELETED 已删除 / ARCHIVED 已归档
     */
    private String status;

    /**
     * 每日预算，单位为分（cent）
     */
    private Integer dailyBudget;

    /**
     * 总预算（分）
     */
    private Integer lifetimeBudget;

    /**
     * 定价策略
     */
    private String bidStrategy;

    /**
     * 所属系列ID
     */
    private String campaignId;


    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String deliveryStatus;
}