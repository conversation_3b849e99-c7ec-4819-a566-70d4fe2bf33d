/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.AppealOrderCardStatusEnum;
import top.continew.admin.biz.enums.AppealOrderStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 申诉订单查询条件
 *
 * <AUTHOR>
 * @since 2025/01/16 11:24
 */
@Data
@Schema(description = "申诉订单查询条件")
public class AppealOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @Query(type = QueryType.EQ)
    private String orderNo;

    /**
     * 客户
     */
    @Schema(description = "客户")
    @Query(type = QueryType.EQ)
    private Long customerId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @QueryIgnore
    private String platformAdId;

    @Schema(description = "bm id")
    @Query(type = QueryType.LIKE, columns = "a.bm_id")
    private String bmId;

    /**
     * 申诉状态
     */
    @Schema(description = "申诉状态")
    @Query(type = QueryType.EQ, columns = "o.status")
    private AppealOrderStatusEnum status;

    /**
     * 卡台状态
     */
    @Schema(description = "卡台状态")
    @Query(type = QueryType.EQ)
    private AppealOrderCardStatusEnum cardStatus;

    @Schema(description = "备注")
    @QueryIgnore
    private String remark;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    @Query(type = QueryType.EQ)
    private Long handleUser;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN, columns = "o.create_time")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;

    /**
     * 卡台状态
     */
    @Schema(description = "bm状态")
    @Query(type = QueryType.EQ, columns = "bm.status")
    private BusinessManagerStatusEnum bmStatus;

    @Schema(description = "清零状态")
    @Query(type = QueryType.EQ, columns = "a.clear_status")
    private AdAccountClearStatusEnum clearStatus;

    @Query(type = QueryType.EQ, columns = "bm.status")
    private Integer isBan;
}