package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.annotation.QueryIgnore;
import top.continew.starter.data.core.enums.QueryType;

/**
 * bm管理员查询条件
 *
 * <AUTHOR>
 * @since 2025/04/22 16:45
 */
@Data
@Schema(description = "bm管理员查询条件")
public class BusinessManagerUserQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * BM ID
     */
    @Schema(description = "BM ID")
    @Query(type = QueryType.EQ)
    private String bmId;

    @Query(type = QueryType.EQ, columns = "bm.type")
    private BusinessManagerTypeEnum bmType;

    /**
     * 是否移除
     */
    @Schema(description = "是否移除")
    @Query(type = QueryType.EQ)
    private Boolean isRemove;

    @Schema(description = "是否自己的号")
    @QueryIgnore
    private Boolean isSelfUser;

    @Query(type = QueryType.EQ)
    private String userRole;

    @Query(type = QueryType.BETWEEN, columns = "bm.create_time")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] bmCreateTime;
}