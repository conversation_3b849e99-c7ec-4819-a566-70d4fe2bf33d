/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 清零订单查询条件
 *
 * <AUTHOR>
 * @since 2024/12/31 11:17
 */
@Data
@Schema(description = "清零订单查询条件")
public class ClearOrderQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    @Query(type = QueryType.EQ, columns = {"o.order_no"})
    private String orderNo;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @Query(type = QueryType.EQ, columns = {"o.customer_id"})
    private Long customerId;

    /**
     * 关联广告户
     */
    @Schema(description = "关联广告户")
    @Query(type = QueryType.EQ, columns = {"o.platform_ad_id"})
    private String platformAdId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @Query(type = QueryType.EQ, columns = {"o.status"})
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Query(type = QueryType.BETWEEN, columns = {"o.create_time"})
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] createTime;

    @Schema(description = "处理人")
    @Query(type = QueryType.EQ, columns = {"o.handle_user"})
    private Long handleUser;
}