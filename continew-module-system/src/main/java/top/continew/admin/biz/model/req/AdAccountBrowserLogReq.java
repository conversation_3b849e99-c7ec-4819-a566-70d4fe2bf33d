/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改账号浏览器操作记录参数
 *
 * <AUTHOR>
 * @since 2024/12/31 14:27
 */
@Data
@Schema(description = "创建或修改账号浏览器操作记录参数")
public class AdAccountBrowserLogReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 64, message = "名称长度不能超过 {max} 个字符")
    private String name;

    /**
     * 标签
     */
    @Schema(description = "标签")
    @NotBlank(message = "标签不能为空")
    @Length(max = 64, message = "标签长度不能超过 {max} 个字符")
    private String label;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    @NotNull(message = "操作时间不能为空")
    private Long opsTime;

    /**
     * 平台账号ID
     */
    @Schema(description = "平台账号ID")
    @NotBlank(message = "平台账号ID不能为空")
    @Length(max = 64, message = "平台账号ID长度不能超过 {max} 个字符")
    private String platformAccountId;

    /**
     * 激活码
     */
    @Schema(description = "激活码")
    @NotBlank(message = "激活码不能为空")
    @Length(max = 20, message = "激活码长度不能超过 {max} 个字符")
    private String activeCode;

    /**
     * 操作环境
     */
    @Schema(description = "操作环境")
    @NotBlank(message = "操作环境不能为空")
    @Length(max = 255, message = "操作环境长度不能超过 {max} 个字符")
    private String env;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;
}