/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改广告户关联卡参数
 *
 * <AUTHOR>
 * @since 2025/01/02 11:24
 */
@Data
@Schema(description = "创建或修改广告户关联卡参数")
public class AdAccountCardReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联广告户
     */
    @Schema(description = "关联广告户")
    @NotNull(message = "关联广告户不能为空")
    private Long adAccountId;

    /**
     * 完整卡号
     */
    @Schema(description = "完整卡号")
    @NotBlank(message = "完整卡号不能为空")
    @Length(max = 64, message = "完整卡号长度不能超过 {max} 个字符")
    private String fullCardNumber;

    /**
     * 模糊卡号
     */
    @Schema(description = "模糊卡号")
    @NotBlank(message = "模糊卡号不能为空")
    @Length(max = 64, message = "模糊卡号长度不能超过 {max} 个字符")
    private String fuzzyCardNumber;

    /**
     * 是否默认
     */
    @Schema(description = "是否默认")
    @NotNull(message = "是否默认不能为空")
    private Boolean isDefault;

    /**
     * 是否已移除
     */
    @Schema(description = "是否已移除")
    @NotNull(message = "是否已移除不能为空")
    private Boolean isRemove;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;
}