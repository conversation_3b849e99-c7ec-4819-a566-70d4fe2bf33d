package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "广告户诊断请求")
public class AdAccountDoctorCheckReq {
    @NotEmpty(message = "广告户ID不能为空")
    @Schema(description = "广告户ID")
    private List<String> adAccountIds;
}