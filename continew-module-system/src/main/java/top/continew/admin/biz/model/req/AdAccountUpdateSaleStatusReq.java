
package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;

@Data
public class AdAccountUpdateSaleStatusReq {
    @NotEmpty(message = "ID不能为空")
    private List<Long> ids;
    
    @NotNull(message = "出售状态不能为空")
    private AdAccountSaleStatusEnum saleStatus;
}