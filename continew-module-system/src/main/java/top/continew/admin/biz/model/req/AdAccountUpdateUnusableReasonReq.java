package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountUnusableReasonEnum;

import java.util.List;

@Data
@Schema(description = "修改不可用原因请求")
public class AdAccountUpdateUnusableReasonReq {

    @Schema(description = "广告账号ID列表")
    @NotEmpty(message = "广告账号ID列表不能为空")
    private List<Long> ids;

    @Schema(description = "不可用原因")
    @NotNull(message = "不可用原因不能为空")
    private AdAccountUnusableReasonEnum unusableReason;
}