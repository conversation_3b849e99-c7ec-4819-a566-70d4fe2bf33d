package top.continew.admin.biz.model.req;

import java.io.Serial;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改指纹浏览器参数
 *
 * <AUTHOR>
 * @since 2025/04/25 17:15
 */
@Data
@Schema(description = "创建或修改指纹浏览器参数")
public class AdsPowerReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * user_id
     */
    @Schema(description = "user_id")
    @NotBlank(message = "user_id不能为空")
    @Length(max = 20, message = "user_id长度不能超过 {max} 个字符")
    private String userId;

    /**
     * 编号
     */
    @Schema(description = "编号")
    @NotBlank(message = "编号不能为空")
    @Length(max = 20, message = "编号长度不能超过 {max} 个字符")
    private String serialNumber;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @NotBlank(message = "备注不能为空")
    @Length(max = 512, message = "备注长度不能超过 {max} 个字符")
    private String remark;
}