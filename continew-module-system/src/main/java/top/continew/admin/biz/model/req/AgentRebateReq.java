package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改中介返点参数
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Data
@Schema(description = "创建或修改中介返点参数")
public class AgentRebateReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联中介
     */
    @Schema(description = "关联中介")
    @NotNull(message = "关联中介不能为空")
    private Long agentId;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @NotNull(message = "关联客户不能为空")
    private Long customerId;

    /**
     * 结算月份
     */
    @Schema(description = "结算月份")
    @NotBlank(message = "结算月份不能为空")
    @Length(max = 20, message = "结算月份长度不能超过 {max} 个字符")
    private String settleMonth;

    /**
     * 结算金额
     */
    @Schema(description = "结算金额")
    @NotNull(message = "结算金额不能为空")
    private BigDecimal settleAmount;

    /**
     * 开户费
     */
    @Schema(description = "开户费")
    @NotNull(message = "开户费不能为空")
    private BigDecimal accountOpenAmount;

    /**
     * 返点金额
     */
    @Schema(description = "返点金额")
    @NotNull(message = "返点金额不能为空")
    private BigDecimal rebateAmount;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;

    private String rebateRule;
}