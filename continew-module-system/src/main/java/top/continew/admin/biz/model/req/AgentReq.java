package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改中介参数
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Data
@Schema(description = "创建或修改中介参数")
public class AgentReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 64, message = "名称长度不能超过 {max} 个字符")
    private String name;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @NotNull(message = "关联商务不能为空")
    private Long businessUserId;

    /**
     * 返点规则
     */
    @Schema(description = "返点规则")
    private String rebateRule;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;
}