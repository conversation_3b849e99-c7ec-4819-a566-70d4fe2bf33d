package top.continew.admin.biz.model.req;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AnalyzeForRemoveUsersReq {

    private String adId;

    private List<AnalyzeForRemoveUsersItemReq> users;


    @Getter
    @Setter
    public static class AnalyzeForRemoveUsersItemReq {
        private String id;

        private String name;

        private Boolean needDelete;
    }
}
