package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;

import java.math.BigDecimal;

@Data
public class BmItemReq {


    @NotNull
    private Long bmId;

    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    @NotNull(message = "类型不能为空")
    private BusinessManagerTypeEnum type;

    @NotNull(message = "渠道不能为空")
    private Long channelId;

    @NotNull(message = "权限不能为空")
    private Integer ownMethod;

    @NotNull(message = "数量不能为空")
    private Integer num;

    private String remark;
}
