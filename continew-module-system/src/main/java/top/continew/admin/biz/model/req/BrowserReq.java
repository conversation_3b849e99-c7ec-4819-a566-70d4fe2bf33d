/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import com.alibaba.fastjson2.JSONObject;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class BrowserReq {
    @NotBlank(message = "name不能为空")
    private String name;

    @NotBlank(message = "label不能为空")
    private String label;

    private Object data;

    private Long ts;

    private String fbAccountId;

    private JSONObject env;

    @NotBlank(message = "activeCode不能为空")
    private String activeCode;

    public String getBrowserNo() {
        if (env == null) {
            return null;
        }
        return env.getString("serial_number");
    }

    public String getBrowserId() {
        if (env == null) {
            return null;
        }
        return env.getString("user_id");
    }

    public String getRemarkFbAccountId() {
        if (env == null) {
            return null;
        }
        return env.getString("name");
    }
}
