/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * BM封禁参数
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
@Data
@Schema(description = "BM封禁参数")
public class BusinessManagerBannedReasonReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;


    private BusinessManagerBannedReasonEnum reason;

}