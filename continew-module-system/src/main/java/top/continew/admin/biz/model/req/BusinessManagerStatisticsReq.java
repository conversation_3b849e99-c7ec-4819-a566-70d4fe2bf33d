package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;


import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改成本分析参数
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Data
@Schema(description = "创建或修改成本分析参数")
public class BusinessManagerStatisticsReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;
}