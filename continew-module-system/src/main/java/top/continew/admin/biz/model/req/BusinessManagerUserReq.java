package top.continew.admin.biz.model.req;

import java.io.Serial;


import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改bm管理员参数
 *
 * <AUTHOR>
 * @since 2025/04/22 16:45
 */
@Data
@Schema(description = "创建或修改bm管理员参数")
public class BusinessManagerUserReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;
}