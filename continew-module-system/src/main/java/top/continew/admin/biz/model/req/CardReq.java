/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardStatusEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改卡片参数
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
@Data
@Schema(description = "创建或修改卡片参数")
public class CardReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡号
     */
    @Schema(description = "卡号")
    @NotBlank(message = "卡号不能为空")
    @Length(max = 64, message = "卡号长度不能超过 {max} 个字符")
    private String cardNumber;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    @NotNull(message = "所属平台不能为空")
    private CardPlatformEnum platform;

    /**
     * 余额
     */
    @Schema(description = "余额")
    @NotNull(message = "余额不能为空")
    private BigDecimal balance;

    /**
     * 状态(1=正常，2=锁定，3=冻结）
     */
    @Schema(description = "状态(1=正常，2=锁定，3=冻结）")
    @NotNull(message = "状态(1=正常，2=锁定，3=冻结）不能为空")
    private CardStatusEnum status;

    /**
     * 第三方平台ID
     */
    @Schema(description = "第三方平台ID")
    @NotBlank(message = "第三方平台ID不能为空")
    @Length(max = 64, message = "第三方平台ID长度不能超过 {max} 个字符")
    private String platformCardId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;
}