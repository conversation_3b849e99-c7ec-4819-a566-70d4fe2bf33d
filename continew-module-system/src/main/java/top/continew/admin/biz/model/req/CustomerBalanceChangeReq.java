/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import top.continew.admin.biz.enums.RechargeFeeHandleMethodEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
public class CustomerBalanceChangeReq {

    @NotNull(message = "动作不能为空")
    private Integer action;

    @NotNull(message = "金额不能为空")
    @Min(value = 0, message = "金额必须大于0")
    private BigDecimal amount;

    private BigDecimal fee;

    private String remark;

    private RechargeFeeHandleMethodEnum feeHandleMethod;

    /**
     * 交易时间
     */
    private LocalDateTime transTime;

    /**
     * 水单凭证
     */
    private String certificate;
}
