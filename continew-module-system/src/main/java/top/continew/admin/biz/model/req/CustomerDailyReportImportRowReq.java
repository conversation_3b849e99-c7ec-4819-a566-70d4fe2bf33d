/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;
import java.math.BigDecimal;

@Data
@Schema(description = "客户日报数据")
public class CustomerDailyReportImportRowReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty("日期")
    private String date;

    @ExcelProperty("归属客户")
    private String customerName;

    @ExcelProperty("广告户ID")
    private String platformAdId;

    @ExcelProperty("状态")
    private String adStatus;

    @ExcelProperty("账户充值金额")
    private BigDecimal adAccountRechargeAmount;

    @ExcelProperty("打款")
    private BigDecimal transferAmount;

    @ExcelProperty("服务费")
    private BigDecimal fee;

    @ExcelProperty("开户费")
    private BigDecimal buyAdAccountAmount;

    @ExcelProperty("取款金额")
    private BigDecimal withdrawAmount;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("退款")
    private BigDecimal refundAmount;

}
