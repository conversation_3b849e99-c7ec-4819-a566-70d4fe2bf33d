package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改客户下户订单分组参数
 *
 * <AUTHOR>
 * @since 2025/07/17 17:14
 */
@Data
@Schema(description = "创建或修改客户下户订单分组参数")
public class CustomerOrderGroupReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "客户ID")
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    private Long groupId;

}