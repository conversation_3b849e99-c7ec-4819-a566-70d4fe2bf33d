package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改客户问题参数
 *
 * <AUTHOR>
 * @since 2025/04/30 14:35
 */
@Data
@Schema(description = "创建或修改客户问题参数")
public class CustomerQuestionReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 问题数量
     */
    @Schema(description = "问题数量")
    @NotNull(message = "问题数量不能为空")
    private Integer num;

    /**
     * 问题账号
     */
    @Schema(description = "问题账号")
    @NotBlank(message = "问题账号不能为空")
    @Length(max = 255, message = "问题账号长度不能超过 {max} 个字符")
    private String accounts;

    @NotNull(message = "下户日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
    private LocalDateTime orderTime;

    private String remark;
}