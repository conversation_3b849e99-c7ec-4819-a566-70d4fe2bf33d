package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.CustomerRequirementStatusEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改客户需求参数
 *
 * <AUTHOR>
 * @since 2025/02/20 14:03
 */
@Data
@Schema(description = "创建或修改客户需求参数")
public class CustomerRequirementReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    /**
     * 时区
     */
    @Schema(description = "时区")
    @NotBlank(message = "时区不能为空")
    @Length(max = 64, message = "时区长度不能超过 {max} 个字符")
    private String timezone;

    /**
     * 需求时间
     */
    @Schema(description = "需求时间")
    @NotNull(message = "需求时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate requirementTime;

    /**
     * 需求数量
     */
    @Schema(description = "需求数量")
    @NotNull(message = "需求数量不能为空")
    private Integer quantity;

    @Schema(description = "状态")
    private CustomerRequirementStatusEnum status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;


    /**
     * 广告户名称
     */
    private String adAccountName;

    /**
     * bm类型
     */
    private Integer bmType;

    /**
     * 客户BM ID
     */
    private String customerBmId;

    /**
     * 开户费
     */
    private BigDecimal payAmount;




}