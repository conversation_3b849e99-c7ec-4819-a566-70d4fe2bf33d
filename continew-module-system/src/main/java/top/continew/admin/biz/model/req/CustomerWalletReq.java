package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改客户钱包参数
 *
 * <AUTHOR>
 * @since 2025/07/22 16:58
 */
@Data
@Schema(description = "创建或修改客户钱包参数")
public class CustomerWalletReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @NotNull(message = "关联客户不能为空")
    private Long customerId;

    /**
     * 钱包地址
     */
    @Schema(description = "钱包地址")
    @NotBlank(message = "钱包地址不能为空")
    @Length(max = 64, message = "钱包地址长度不能超过 {max} 个字符")
    private String walletAddress;
}