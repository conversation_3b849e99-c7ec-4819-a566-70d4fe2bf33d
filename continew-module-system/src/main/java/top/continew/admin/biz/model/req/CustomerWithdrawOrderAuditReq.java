package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;
import top.continew.admin.biz.enums.CustomerWithdrawOrderStatusEnum;


@Data
@Schema(description = "客户余额提现订单审核请求")
public class CustomerWithdrawOrderAuditReq {
    @NotNull(message = "订单ID不能为空")
    @Schema(description = "订单ID")
    private Long id;

    @NotNull(message = "审核状态不能为空")
    @Schema(description = "审核状态")
    private CustomerWithdrawOrderStatusEnum status;

    @NotNull(message = "审核备注不能为空")
    @Schema(description = "审核备注")
    @Length(max = 500, message = "备注长度不能超过 {max} 个字符")
    private String remark;
}
