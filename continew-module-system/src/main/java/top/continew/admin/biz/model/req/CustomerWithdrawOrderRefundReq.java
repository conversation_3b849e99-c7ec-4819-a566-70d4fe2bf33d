package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "客户余额提现订单退款请求")
public class CustomerWithdrawOrderRefundReq {
    @NotNull(message = "订单ID不能为空")
    @Schema(description = "订单ID")
    private Long id;

    @NotNull(message = "退款时间不能为空")
    @Schema(description = "退款时间")
    private LocalDateTime actualRefundTime;

    @NotNull(message = "退款金额不能为空")
    @Schema(description = "退款金额")
    private BigDecimal actualRefundAmount;
}
