package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改客户余额提现订单参数
 *
 * <AUTHOR>
 * @since 2025/01/22 14:23
 */
@Data
@Schema(description = "创建或修改客户余额提现订单参数")
public class CustomerWithdrawOrderReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户ID
     */
    @Schema(description = "关联客户ID")
    @NotNull(message = "关联客户ID不能为空")
    private Long customerId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 500, message = "备注长度不能超过 {max} 个字符")
    private String remark;
}