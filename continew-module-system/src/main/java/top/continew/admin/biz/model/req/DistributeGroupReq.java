package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class DistributeGroupReq {

    @NotEmpty(message = "ids不能为空")
    private List<Long> ids;

    @NotNull(message = "分组ID不能为空")
    private Long groupId;

    @NotNull(message = "客户ID")
    private Long customerId;
}
