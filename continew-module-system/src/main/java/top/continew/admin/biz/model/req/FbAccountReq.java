/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import top.continew.admin.biz.enums.FbAccountStatusEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;

/**
 * 创建或修改fb账号参数
 *
 * <AUTHOR>
 * @since 2025/01/03 16:32
 */
@Data
@Schema(description = "创建或修改fb账号参数")
public class FbAccountReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联账号ID
     */
    @Schema(description = "关联账号ID")
    @NotBlank(message = "关联账号ID不能为空")
    @Length(max = 64, message = "关联账号ID长度不能超过 {max} 个字符")
    private String platformAccountId;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    private String browserSerialNo;

    /**
     * 状态（1=正常，2=封禁）
     */
    @Schema(description = "状态（1=正常，2=封禁）")
    @NotNull(message = "状态不能为空")
    private FbAccountStatusEnum status;

    private String customRemark;

    private String proxyType;

    private String proxy;
}