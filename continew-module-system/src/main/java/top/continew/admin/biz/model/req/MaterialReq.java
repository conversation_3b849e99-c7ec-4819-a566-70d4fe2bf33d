package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.MaterialTypeEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改物料参数
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
@Data
@Schema(description = "创建或修改物料参数")
public class MaterialReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private LocalDateTime payDate;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @NotNull(message = "类型不能为空")
    private MaterialTypeEnum type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer num;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private Long channelId;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    @NotNull(message = "支付金额不能为空")
    private BigDecimal payPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;
}