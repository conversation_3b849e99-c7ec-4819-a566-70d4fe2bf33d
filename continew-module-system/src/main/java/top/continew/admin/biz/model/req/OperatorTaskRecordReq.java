package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改运营人员工作记录参数
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Data
@Schema(description = "创建或修改运营人员工作记录参数")
public class OperatorTaskRecordReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    @Length(max = 64, message = "广告户长度不能超过 {max} 个字符")
    private String platformAdId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @NotNull(message = "数量不能为空")
    private Integer num;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;

    private Long customerId;
}