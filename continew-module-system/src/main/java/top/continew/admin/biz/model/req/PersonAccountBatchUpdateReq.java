package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import top.continew.admin.biz.enums.PersonalAccountStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAppealStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;

import java.util.List;

@Data
public class PersonAccountBatchUpdateReq {

    @NotEmpty(message = "账号列表不能为空")
    private List<Long> ids;

    private Boolean isChangePwd;

    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    private String afterSaleReason;

    private PersonalAccoutAppealStatusEnum appealStatus;

    private PersonalAccoutTypeEnum type;

    private Boolean isAfterSale;

    private String remark;

    private PersonalAccountStatusEnum accountStatus;

}
