package top.continew.admin.biz.model.req;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import top.continew.admin.biz.enums.PersonalAccountStatusEnum;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 个号详情信息
 *
 * <AUTHOR>
 * @since 2025/02/27 14:48
 */
@Data
@ExcelIgnoreUnannotated
public class PersonalAccountImportExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "购买日期")
    @ExcelProperty(value = "购买日期")
    private LocalDateTime createTime;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    @ExcelProperty(value = "渠道")
    private String channelName;

    /**
     * 账号信息
     */
    @Schema(description = "账号信息")
    @ExcelProperty(value = "三解号")
    private String content;

    /**
     * 接入人
     */
    @Schema(description = "接入人")
    @ExcelProperty(value = "接入操作人")
    private String accessUser;

    /**
     * 是否接入
     */
    @Schema(description = "是否接入")
    @ExcelProperty(value = "是否接入")
    private String access;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    @ExcelProperty(value = "浏览器编号")
    private String browserNo;

    /**
     * 账号状态
     */
    @Schema(description = "状态")
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 单价
     */
    @Schema(description = "单价")
    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
}