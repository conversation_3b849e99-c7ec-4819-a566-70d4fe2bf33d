package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.PersonalAccountStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutAppealStatusEnum;
import top.continew.admin.biz.enums.PersonalAccoutTypeEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 创建或修改个号参数
 *
 * <AUTHOR>
 * @since 2025/02/27 14:48
 */
@Data
@Schema(description = "创建或修改个号参数")
public class PersonalAccountReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    @NotNull(message = "渠道不能为空")
    private Long channelId;

    /**
     * 账号信息
     */
    @Schema(description = "账号信息")
    @NotBlank(message = "账号信息不能为空")
    @Length(max = 1024, message = "账号信息长度不能超过 {max} 个字符")
    private String content;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    private String browserNo;

    /**
     * 账号状态
     */
    @Schema(description = "账号状态")
    @NotNull(message = "账号状态不能为空")
    private PersonalAccountStatusEnum accountStatus;

    /**
     * 单价
     */
    @Schema(description = "单价")
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    private PersonalAccoutTypeEnum type;

    private Boolean isAfterSale;

    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    private String afterSaleReason;

    private PersonalAccoutAppealStatusEnum appealStatus;

    private Boolean isChangePwd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime purchaseTime;

    private String platformAccountId;

    private String uniqueKey;

    private String email;

    private String proxy;

    @NotBlank(message = "浏览器ID不能为空")
    private String browserId;


}