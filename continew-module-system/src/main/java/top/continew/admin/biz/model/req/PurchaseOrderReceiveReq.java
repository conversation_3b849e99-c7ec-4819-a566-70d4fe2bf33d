package top.continew.admin.biz.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.extension.crud.model.req.BaseReq;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 创建或修改采购验收单参数
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@Schema(description = "创建或修改采购验收单参数")
public class PurchaseOrderReceiveReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联采购单
     */
    @Schema(description = "关联采购单")
    @NotNull(message = "关联采购单不能为空")
    private Long id;

    /**
     * 验收数量
     */
    @Schema(description = "验收数量")
    @NotNull(message = "验收数量不能为空")
    private Integer receiveNum;

    /**
     * 验收金额
     */
    @NotNull(message = "验收金额不能为空")
    private BigDecimal receivePrice;

    @NotNull(message = "验收时间不能为空")
    private LocalDate receiveDate;
}