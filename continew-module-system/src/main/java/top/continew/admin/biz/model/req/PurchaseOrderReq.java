package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.PurchaseOrderStatusEnum;
import top.continew.admin.biz.enums.PurchaseOrderTypeEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改采购订单参数
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@Schema(description = "创建或修改采购订单参数")
public class PurchaseOrderReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    @NotNull(message = "渠道ID不能为空")
    private Long channelId;

    /**
     * 物料类型
     */
    @Schema(description = "物料类型")
    @NotNull(message = "物料类型不能为空")
    private PurchaseOrderTypeEnum type;

    /**
     * 预计采购数量
     */
    @Schema(description = "预计采购数量")
    @NotNull(message = "预计采购数量不能为空")
    private Integer expectNum;

    /**
     * 预计采购金额
     */
    @Schema(description = "预计采购金额")
    @NotNull(message = "预计采购金额不能为空")
    private BigDecimal totalPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 255, message = "备注长度不能超过 {max} 个字符")
    private String remark;


    private Integer receiveNum;

    private BigDecimal receivePrice;

    private String receiveUser;

    private PurchaseOrderStatusEnum status;

    private LocalDate purchaseTime;

    private LocalDate receiveDate;
}