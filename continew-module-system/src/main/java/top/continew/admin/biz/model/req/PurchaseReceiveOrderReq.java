package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改采购验收单参数
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@Schema(description = "创建或修改采购验收单参数")
public class PurchaseReceiveOrderReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联采购单
     */
    @Schema(description = "关联采购单")
    @NotNull(message = "关联采购单不能为空")
    private Long purchaseOrderId;

    /**
     * 验收数量
     */
    @Schema(description = "验收数量")
    @NotNull(message = "验收数量不能为空")
    private Integer receiveNum;

    /**
     * 验收金额
     */
    @NotNull(message = "验收金额不能为空")
    private BigDecimal receivePrice;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "验收时间不能为空")
    private LocalDateTime receiveTime;
}