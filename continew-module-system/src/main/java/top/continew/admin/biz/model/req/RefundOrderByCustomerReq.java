package top.continew.admin.biz.model.req;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RefundOrderByCustomerReq {

    @NotNull(message = "客户ID不能为空")
    private Long customerId;


    @NotNull(message = "减款金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "减款金额必须大于0")
    private BigDecimal amount;

    @NotNull(message = "广告户ID不能为空")
    private String adAccountId;
}
