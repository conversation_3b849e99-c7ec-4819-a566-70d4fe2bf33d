package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改结算订单详情参数
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Data
@Schema(description = "创建或修改结算订单详情参数")
public class SettleOrderItemReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联订单
     */
    @Schema(description = "关联订单")
    @NotNull(message = "关联订单不能为空")
    private Long orderId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @NotBlank(message = "广告户ID不能为空")
    @Length(max = 64, message = "广告户ID长度不能超过 {max} 个字符")
    private String platformAdId;

    /**
     * 消耗
     */
    @Schema(description = "消耗")
    @NotNull(message = "消耗不能为空")
    private BigDecimal spent;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;
}