package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改标签关联参数
 *
 * <AUTHOR>
 * @since 2025/05/08 16:37
 */
@Data
@Schema(description = "创建或修改标签关联参数")
public class TagRelationReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @Schema(description = "标签ID")
    @NotNull(message = "标签ID不能为空")
    private Long tagId;

    /**
     * 类型(1=广告户 2=下户订单)
     */
    @Schema(description = "类型(1=广告户 2=下户订单)")
    @NotNull(message = "类型(1=广告户 2=下户订单)不能为空")
    private Integer type;

    /**
     * 关联ID
     */
    @Schema(description = "关联ID")
    @NotNull(message = "关联ID不能为空")
    private Long relationId;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    @NotNull(message = "创建日期不能为空")
    private LocalDateTime createTime;
}