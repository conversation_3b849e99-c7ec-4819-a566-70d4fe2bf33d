package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;
import java.util.List;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改测试任务详情参数
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@Schema(description = "创建或修改测试任务详情参数")
public class TestOrderItemReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "任务详情列表不能为空")
    private List<Long> ids;


    private Integer status;


    private String remark;
}