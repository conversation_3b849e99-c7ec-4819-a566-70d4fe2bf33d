package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;


import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改钱包流水参数
 *
 * <AUTHOR>
 * @since 2025/07/22 15:59
 */
@Data
@Schema(description = "创建或修改钱包流水参数")
public class WalletTransferReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;
}