package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 客户社交账号添加请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "客户社交账号添加请求")
public class CustomerAccountAddReq {

    @Schema(description = "账号类型：对应着社交账号类型")
    @NotNull(message = "账号类型不能为空")
    private Integer accountType;

    @Schema(description = "账号")
    @NotBlank(message = "账号不能为空")
    private String account;

    @Schema(description = "关联的社交账号ID")
    private Long socialAccountId;

    @Schema(description = "实体类型：1-线索、2-商机、3-客户")
    private Integer entityType;

    @Schema(description = "实体ID")
    private Long entityId;

}