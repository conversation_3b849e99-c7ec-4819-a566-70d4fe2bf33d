package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 客户标签添加请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "客户标签添加请求")
public class CustomerTagAddReq {
    
    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空")
    @Schema(description = "客户ID")
    private Long customerId;
    
    /**
     * 标签ID列表
     */
    @NotNull(message = "标签不能为空")
    @Schema(description = "标签")
    private Long tagId;
    
    /**
     * 标签类型编码，对应着字典类型编码
     */
    @NotNull(message = "标签类型不能为空")
    @Schema(description = "标签类型，对应着字典类型编码")
    private String code;
}