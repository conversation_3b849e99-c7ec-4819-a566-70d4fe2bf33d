package top.continew.admin.biz.model.req.crm;

import lombok.Data;

import java.util.List;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/6/15 16:20
 */
@Data
public class CustomerVisitStragegyBasicConditionReq {

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 销售标签ID列表
     */
    private List<String> salesTagIds;

    /**
     * 是否退款客户
     */
    private Boolean isRefund;
}
