package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改客户回访策略参数
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Data
@Schema(description = "创建或修改客户回访策略参数")
public class CustomerVisitStrategyReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 策略名称
     */
    @Schema(description = "策略名称")
    @NotBlank(message = "策略名称不能为空")
    @Length(max = 100, message = "策略名称长度不能超过 {max} 个字符")
    private String strategyName;

    @Schema(description = "策略描述")
    private String strategyDesc;

    /**
     * 适用客户类型：1-潜在客户，2-正式客户
     */
    @Schema(description = "适用客户类型：1-潜在客户，2-正式客户")
    @NotNull(message = "适用客户类型：1-潜在客户，2-正式客户不能为空")
    private Integer customerType;

    /**
     * 策略状态：0-禁用，1-启用
     */
    @Schema(description = "策略状态：0-禁用，1-启用")
    @NotNull(message = "策略状态：0-禁用，1-启用不能为空")
    private Integer strategyStatus;

    /**
     * 策略条件配置(JSON格式)
     */
    @Schema(description = "策略条件配置(JSON格式)")
    @NotBlank(message = "策略条件配置(JSON格式)不能为空")
    private String strategyConditions;
}