package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import top.continew.admin.biz.enums.VisitTaskStatusEnum;

import java.time.LocalDateTime;

/**
 * 客户回访任务明细请求信息
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
@Schema(description = "客户回访任务明细请求信息")
public class CustomerVisitTaskDetailReq {

    /**
     * 回访任务ID
     */
    @Schema(description = "回访任务ID", example = "1")
    @NotNull(message = "回访任务ID不能为空")
    private Long taskId;

    /**
     * 回访方式
     */
    @Schema(description = "回访方式", example = "电话")
    @NotBlank(message = "回访方式不能为空")
    private String visitMethod;

    /**
     * 回访时间
     */
    @Schema(description = "回访时间")
    @NotNull(message = "回访时间不能为空")
    private LocalDateTime visitTime;

    /**
     * 回访纪要
     */
    @Schema(description = "回访纪要")
    private String visitSummary;

    /**
     * 附件URL
     */
    @Schema(description = "附件URL")
    private String attachmentUrls;

    private VisitTaskStatusEnum taskStatus;
    private Integer visitResult;
    private LocalDateTime nextReminderTime;
}