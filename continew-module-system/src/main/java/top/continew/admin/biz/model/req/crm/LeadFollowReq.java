package top.continew.admin.biz.model.req.crm;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.LeadStatusEnum;

import java.time.LocalDateTime;

/**
 * 线索跟进记录请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "线索跟进记录请求")
public class LeadFollowReq {

    /**
     * 线索ID
     */
    @NotNull(message = "线索ID不能为空")
    @Schema(description = "线索ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long leadId;

    /**
     * 线索状态
     */
    @Schema(description = "线索状态：1-待跟进、2-跟进中、3-长期跟进、4-创建商机、5-无效")
    @NotNull(message = "跟进状态不能为空")
    private LeadStatusEnum status;

    /**
     * 跟进时间
     */
    @NotNull(message = "跟进时间不能为空")
    @Schema(description = "跟进时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime followTime;

    /**
     * 跟进内容
     */
    private String content;


    /**
     * 附件地址
     */
    @Schema(description = "附件地址")
    private String attachment;




    /**
     * 提醒时间
     */
    @Schema(description = "提醒时间，长期跟进时传递")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime remindTime;

    /**
     * 无效原因
     */
    @Schema(description = "无效原因，无效时传递")
    private Integer invalidReason;

    @Schema(description = "分派的商务用户ID")
    private Long businessUserId;

    @Schema(description = "TG群ID")
    private String telegramChatId;


}