package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;
import java.util.List;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改线索参数
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "创建或修改线索参数")
public class LeadReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源
     */
    @Schema(description = "来源")
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-创建商机、5-无效
     */
    @Schema(description = "状态：1-待跟进、2-跟进中、3-长期跟进、4-创建商机、5-无效")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    @Length(max = 100, message = "客户名称长度不能超过 {max} 个字符")
    private String customerName;

    /**
     * 客户行业
     */
    @Schema(description = "客户行业")
    private Integer customerIndustry;

    /**
     * 需求内容
     */
    @Schema(description = "需求内容")
    @NotBlank(message = "需求内容不能为空")
    @Length(max = 5000, message = "需求内容长度不能超过 {max} 个字符")
    private String requirement;

    /**
     * 对接人
     */
    @Schema(description = "对接人")
    private Long handlerUserId;

    private List<CustomerAccountAddReq> accounts;


    @Schema(description = "公司名称")
    private String companyName;
    @Schema(description = "客户职位")
    private String customerPosition;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "团队规模")
    private String teamSize;
    @Schema(description = "团队单日消耗(美元)")
    private String dailyTeamSpending;
    @Schema(description = "产品名称")
    private String productName;

}