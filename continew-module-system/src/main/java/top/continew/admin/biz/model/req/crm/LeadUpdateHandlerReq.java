package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量修改线索对接人请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "批量修改线索对接人请求")
public class LeadUpdateHandlerReq {

    /**
     * 线索ID列表
     */
    @NotEmpty(message = "线索ID列表不能为空")
    @Schema(description = "线索ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> ids;

    /**
     * 对接人ID
     */
    @NotNull(message = "对接人ID不能为空")
    @Schema(description = "对接人ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long handlerUserId;
}