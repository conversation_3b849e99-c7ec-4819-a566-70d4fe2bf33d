package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import top.continew.admin.biz.enums.OpportunityStatusEnum;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商机跟进记录请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "商机跟进记录请求")
public class OpportunityFollowReq {

    @Schema(description = "商机ID")
    @NotNull(message = "商机ID不能为空")
    private Long opportunityId;

    @Schema(description = "跟进内容")
    private String content;

    @Schema(description = "附件")
    private String attachment;

    @Schema(description = "跟进时间")
    @NotNull(message = "跟进时间不能为空")
    private LocalDateTime followTime;

    @Schema(description = "商机状态")
    @NotNull(message = "商机状态不能为空")
    private OpportunityStatusEnum status;

    @Schema(description = "提醒时间")
    private LocalDateTime remindTime;

    @Schema(description = "流失原因")
    private Integer invalidReason;

    private Long followUserId;

}