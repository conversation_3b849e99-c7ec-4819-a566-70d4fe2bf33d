package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商机跟进记录更新请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "商机跟进记录更新请求")
public class OpportunityFollowUpdateReq {

    @Schema(description = "跟进记录ID")
    @NotNull(message = "跟进记录ID不能为空")
    private Long id;

    @Schema(description = "跟进内容")
    private String content;

    @Schema(description = "附件")
    private String attachment;

    @Schema(description = "跟进时间")
    @NotNull(message = "跟进时间不能为空")
    private LocalDateTime followTime;

    /**
     * 提醒时间
     */
    @Schema(description = "提醒时间")
    private LocalDateTime remindTime;

    /**
     * 无效原因
     */
    @Schema(description = "无效原因")
    private Integer invalidReason;
}