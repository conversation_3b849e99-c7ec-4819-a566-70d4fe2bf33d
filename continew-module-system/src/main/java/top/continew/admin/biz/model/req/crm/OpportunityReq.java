package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;
import java.util.List;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.OpportunityStatusEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改商机参数
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "创建或修改商机参数")
public class OpportunityReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户ID
     */
    @Schema(description = "关联客户ID")
    @NotNull(message = "关联客户ID不能为空")
    private Long customerId;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    @NotNull(message = "来源ID不能为空")
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失
     */
    @Schema(description = "状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失")
    @NotNull(message = "状态不能为空")
    private OpportunityStatusEnum status;

    /**
     * 需求内容
     */
    @Schema(description = "需求内容")
    @NotBlank(message = "需求内容不能为空")
    @Length(max = 5000, message = "需求内容长度不能超过 {max} 个字符")
    private String requirement;

    /**
     * 流失原因
     */
    @Schema(description = "流失原因")
    @NotNull(message = "流失原因不能为空")
    private Integer lostReason;

    /**
     * 对接人
     */
    @Schema(description = "对接人")
    @NotNull(message = "对接人不能为空")
    private Long handlerUserId;


    private List<CustomerAccountAddReq> accounts;

    @Schema(description = "公司名称")
    private String companyName;
    @Schema(description = "客户职位")
    private String customerPosition;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "团队规模")
    private String teamSize;
    @Schema(description = "团队单日消耗(美元)")
    private String dailyTeamSpending;
    @Schema(description = "产品名称")
    private String productName;

}