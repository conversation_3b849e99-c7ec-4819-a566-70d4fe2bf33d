package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改社交账号参数
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@Schema(description = "创建或修改社交账号参数")
public class SocialAccountReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账号类型：telegram、wechat、line、phone
     */
    @Schema(description = "账号类型：telegram、wechat、line、phone")
    @NotNull(message = "账号类型不能为空")
    private Integer accountType;

    /**
     * 账号
     */
    @Schema(description = "账号")
    @NotBlank(message = "账号不能为空")
    @Length(max = 200, message = "账号长度不能超过 {max} 个字符")
    private String account;

    /**
     * 分配人
     */
    @Schema(description = "分配人")
    private Long assigneeId;

    /**
     * 账号状态：1-启用、2-禁用
     */
    @Schema(description = "账号状态：1-启用、2-禁用")
    @NotNull(message = "账号状态不能为空")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}