package top.continew.admin.biz.model.req.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 批量更新状态请求
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@Schema(description = "批量更新状态请求")
public class SocialAccountUpdateStatusReq {

    @NotEmpty(message = "ID列表不能为空")
    @Schema(description = "ID列表")
    private List<Long> ids;

    @NotNull(message = "状态不能为空")
    @Schema(description = "状态")
    private Integer status;
}