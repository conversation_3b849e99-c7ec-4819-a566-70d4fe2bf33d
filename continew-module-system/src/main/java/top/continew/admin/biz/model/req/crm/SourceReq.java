package top.continew.admin.biz.model.req.crm;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改来源参数
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@Schema(description = "创建或修改来源参数")
public class SourceReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源分组ID
     */
    @Schema(description = "来源分组ID")
    @NotNull(message = "来源分组ID不能为空")
    private Long groupId;

    /**
     * 来源名称
     */
    @Schema(description = "来源名称")
    @NotBlank(message = "来源名称不能为空")
    @Length(max = 200, message = "来源名称长度不能超过 {max} 个字符")
    private String name;
}