/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardStatusEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 广告户关联卡信息
 *
 * <AUTHOR>
 * @since 2025/01/02 11:24
 */
@Data
@Schema(description = "广告户关联卡信息")
public class AdAccountCardResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联广告户
     */
    @Schema(description = "关联广告户")
    private String platformAdId;

    /**
     * 所属平台
     */
    private CardPlatformEnum platform;

    /**
     * 完整卡号
     */
    @Schema(description = "完整卡号")
    private String fullCardNumber;

    /**
     * 模糊卡号
     */
    @Schema(description = "模糊卡号")
    private String fuzzyCardNumber;

    /**
     * 是否默认
     */
    @Schema(description = "是否默认")
    private Boolean isDefault;

    /**
     * 是否已移除
     */
    @Schema(description = "是否已移除")
    private Boolean isRemove;

    /**
     * 移除时间
     */
    @Schema(description = "移除时间")
    private LocalDateTime removeTime;

    private Long cardId;

    private BigDecimal balance;

    private BigDecimal usedAmount;

    private CardStatusEnum cardStatus;
}