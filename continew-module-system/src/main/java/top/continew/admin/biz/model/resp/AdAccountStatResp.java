/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ExcelIgnoreUnannotated
public class AdAccountStatResp {

    @ExcelProperty("客户")
    private String customerName;

    /**
     * 下户日期
     */
    @ExcelProperty("下户日期")
    private LocalDateTime finishTime;

    @ExcelProperty("下户时区日期")
    private LocalDateTime adAccountFinishTime;

    @ExcelProperty("回收日期")
    private LocalDateTime recycleTime;

    @ExcelProperty("回收时区日期")
    private LocalDateTime adAccountRecycleTime;

    private AdAccountOrderStatusEnum orderStatus;

    @ExcelProperty("广告户")
    private String adAccountId;

    private String browserNo;

    /**
     * 广告户状态
     */
    @ExcelProperty(value = "账号状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountStatusEnum accountStatus;


    /**
     * 时区
     */
    @ExcelProperty("时区")
    private String timezone;

    /**
     * FB面板数据消耗
     */
    @Schema(description = "FB总消耗")
    @ExcelProperty("FB总消耗")
    private BigDecimal adAmount;

    /**
     * FB面板数据扣款
     */
    @Schema(description = "FB总扣款")
    @ExcelProperty("FB总扣款")
    private BigDecimal adAmountDeductions;

    /**
     * 卡台消耗
     */
    @Schema(description = "卡台消耗")
    @ExcelProperty("卡台消耗")
    private BigDecimal cardAmount;


    @ExcelProperty("充值")
    private BigDecimal recharge;

    @ExcelProperty("FB余额")
    private BigDecimal fbBalance;

    /**
     * 差额
     */
    @Schema(description = "差值")
    @ExcelProperty("差额")
    private BigDecimal diffAmount;

}
