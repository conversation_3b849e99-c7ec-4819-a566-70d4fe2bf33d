package top.continew.admin.biz.model.resp;

import java.io.Serial;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 指纹浏览器信息
 *
 * <AUTHOR>
 * @since 2025/04/25 17:15
 */
@Data
@Schema(description = "指纹浏览器信息")
public class AdsPowerResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * user_id
     */
    @Schema(description = "user_id")
    private String userId;

    /**
     * 编号
     */
    @Schema(description = "编号")
    private String serialNumber;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}