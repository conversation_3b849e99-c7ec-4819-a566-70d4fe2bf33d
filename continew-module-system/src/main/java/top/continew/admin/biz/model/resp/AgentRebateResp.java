package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 中介返点信息
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Data
@Schema(description = "中介返点信息")
public class AgentRebateResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联中介
     */
    @Schema(description = "关联中介")
    private Long agentId;

    private String agentName;

    private String businessUserName;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    private Long customerId;

    private String customerName;

    /**
     * 结算月份
     */
    @Schema(description = "结算月份")
    private String settleMonth;

    /**
     * 结算金额
     */
    @Schema(description = "结算金额")
    private BigDecimal settleAmount;

    /**
     * 开户费
     */
    @Schema(description = "开户费")
    private BigDecimal accountOpenAmount;

    /**
     * 返点日期
     */
    @Schema(description = "返点日期")
    private LocalDateTime rebateTime;

    /**
     * 返点金额
     */
    @Schema(description = "返点金额")
    private BigDecimal rebateAmount;

    private BigDecimal actualRebateAmount;

    /**
     * 返点规则
     */
    @Schema(description = "返点规则")
    private String rebateRule;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}