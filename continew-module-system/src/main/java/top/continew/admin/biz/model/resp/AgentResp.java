package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 中介信息
 *
 * <AUTHOR>
 * @since 2025/07/19 11:09
 */
@Data
@Schema(description = "中介信息")
public class AgentResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "businessUserName"))
    private Long businessUserId;

    private String businessUserName;

    /**
     * 返点规则
     */
    @Schema(description = "返点规则")
    private String rebateRule;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}