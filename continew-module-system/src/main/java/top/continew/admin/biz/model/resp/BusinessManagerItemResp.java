package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountSaleStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.admin.biz.excel.converter.BooleanConverter;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * bm坑位信息
 *
 * <AUTHOR>
 * @since 2025/03/11 11:52
 */
@Data
@Schema(description = "bm坑位信息")
@ExcelIgnoreUnannotated
public class BusinessManagerItemResp {

    private Long id;


    @ExcelProperty(value = "BM渠道")
    private String businessManagerChannel;

    private Long businessManagerId;

    @ExcelProperty(value = "BM ID")
    private String businessManager;

    @ExcelProperty(value = "BM类型", converter = ExcelBaseEnumConverter.class)
    private BusinessManagerTypeEnum type;


    /**
     * 名称（坑位1）
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 单价
     */
    @Schema(description = "单价")
    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 状态（1=正常，2=封禁）
     */
    @Schema(description = "状态（1=正常，2=封禁）")
    @ExcelProperty(value = "状态", converter = ExcelBaseEnumConverter.class)
    private BusinessManagerStatusEnum status;

    @ExcelProperty(value = "出售状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountSaleStatusEnum saleStatus;

    @ExcelProperty(value = "出售时间")
    private LocalDateTime saleTime;
    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    @ExcelProperty(value = "广告户ID")
    private String platformAdId;

    /**
     * 是否使用
     */
    @Schema(description = "是否使用")
    @ExcelProperty(value = "是否使用")
    private Boolean isUse;

    /**
     * 使用时间
     */
    @Schema(description = "使用时间")
    @ExcelProperty(value = "使用时间")
    private LocalDateTime useTime;

    /**
     * 封禁时间
     */
    @Schema(description = "封禁时间")
    @ExcelProperty(value = "封禁时间")
    private LocalDateTime banTime;

    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    private String remark;

    private Integer ownMethod;

    @ExcelProperty(value = "是否补号" ,converter = BooleanConverter.class)
    private Boolean isBu;
}