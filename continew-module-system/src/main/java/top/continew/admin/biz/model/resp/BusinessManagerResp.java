/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.BusinessManagerBannedReasonEnum;
import top.continew.admin.biz.enums.BusinessManagerStatusEnum;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.admin.biz.enums.PersonalAccoutAfterSaleStatusEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * BM5账号信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:48
 */
@Data
@Schema(description = "BM5账号信息")
public class BusinessManagerResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联渠道
     */
    @Schema(description = "关联渠道")
    private Long channelId;

    /**
     * BM5 ID
     */
    @Schema(description = "BM5 ID")
    private String platformId;

    /**
     * 浏览器编号
     */
    @Schema(description = "浏览器编号")
    private String browserNo;

    private BusinessManagerStatusEnum status;

    private BusinessManagerBannedReasonEnum bannedReason;

    private Boolean isUse;

    private String remark;

    private String content;

    private Integer num;

    private String user;

    private LocalDateTime useTime;

    private Integer useNum;

    private Integer useBlackNum;

    private BigDecimal unitPrice;

    private LocalDateTime banTime;

    private String opsBrowser;
    private String reserveBrowser;
    private String observeBrowser;


    private PersonalAccoutAfterSaleStatusEnum afterSaleStatus;

    private String afterSaleReason;

    private Integer useItemNum;

    private BusinessManagerTypeEnum type;

    private Boolean isExternal;

    private Boolean isEnterpriseAuth;

    private Boolean isRemoveAdmin;

    private String reserveBrowserBak;

    private Boolean isBu;

    private Boolean drop;
}