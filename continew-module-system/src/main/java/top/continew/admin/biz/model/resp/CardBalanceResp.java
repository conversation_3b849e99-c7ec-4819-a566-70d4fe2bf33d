/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.CardBalanceTypeEnum;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 卡片余额流水信息
 *
 * <AUTHOR>
 * @since 2024/12/29 13:45
 */
@Data
@Schema(description = "卡片余额流水信息")
public class CardBalanceResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡台平台
     */
    @Schema(description = "卡台平台")
    private CardPlatformEnum platform;

    /**
     * 卡号
     */
    @Schema(description = "卡号")
    private String cardNumber;

    /**
     * 类型（1=充值，2=提现）
     */
    @Schema(description = "类型（1=充值，2=提现）")
    private CardBalanceTypeEnum type;

    /**
     * 变更金额
     */
    @Schema(description = "变更金额")
    private BigDecimal amount;

    /**
     * 交易后余额
     */
    @Schema(description = "交易后余额")
    private BigDecimal afterAmount;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transTime;
}