/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.*;
import top.continew.admin.common.base.BaseResp;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡片详情信息
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "卡片详情信息")
public class CardDetailResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡号
     */
    @Schema(description = "卡号")
    @ExcelProperty(value = "卡号")
    private String cardNumber;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    @ExcelProperty(value = "所属平台", converter = ExcelBaseEnumConverter.class)
    private CardPlatformEnum platform;

    /**
     * 余额
     */
    @Schema(description = "余额")
    @ExcelProperty(value = "余额")
    private BigDecimal balance;

    @Schema(description = "已使用金额")
    @ExcelProperty(value = "已使用金额")
    private BigDecimal usedAmount;

    /**
     * 状态(1=正常，2=锁定，3=冻结）
     */
    @Schema(description = "状态(1=正常，2=锁定，3=冻结）")
    @ExcelProperty(value = "卡片状态", converter = ExcelBaseEnumConverter.class)
    private CardStatusEnum status;

    /**
     * 开卡日期
     */
    @Schema(description = "开卡日期")
    @ExcelProperty(value = "开卡日期")
    private LocalDateTime openTime;

    private Long createUser;

    @ExcelProperty(value = "开卡人")
    private String createUserName;

    /**
     * 第三方平台ID
     */
    @Schema(description = "第三方平台ID")
    private String platformCardId;
    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "关联广告户")
    private String platformAdId;

    @ExcelProperty(value = "广告户状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountStatusEnum accountStatus;

    @ExcelProperty(value = "养号状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountKeepStatusEnum keepStatus;

    @ExcelProperty(value = "出售状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountSaleStatusEnum saleStatus;

    @ExcelProperty(value = "清零状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountClearStatusEnum clearStatus;

    @ExcelProperty(value = "关联客户")
    private String relatedCustomer;
}