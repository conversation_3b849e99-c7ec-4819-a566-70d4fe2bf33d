/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.*;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡片信息
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
@Data
@Schema(description = "卡片信息")
public class CardResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡号
     */
    @Schema(description = "卡号")
    private String cardNumber;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    private CardPlatformEnum platform;

    /**
     * 余额
     */
    @Schema(description = "余额")
    private BigDecimal balance;

    /**
     * 状态(1=正常，2=锁定，3=冻结）
     */
    @Schema(description = "状态(1=正常，2=锁定，3=冻结）")
    private CardStatusEnum status;

    /**
     * 开卡日期
     */
    @Schema(description = "开卡日期")
    private LocalDateTime openTime;

    private Long createUser;

    private String createUserName;

    /**
     * 第三方平台ID
     */
    @Schema(description = "第三方平台ID")
    private String platformCardId;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    private String remark;

    private BigDecimal usedAmount;

    private String platformAdId;

    private String relatedCustomer;

    private AdAccountKeepStatusEnum keepStatus;

    private AdAccountStatusEnum accountStatus;

    private AdAccountSaleStatusEnum saleStatus;

    private AdAccountClearStatusEnum clearStatus;
}