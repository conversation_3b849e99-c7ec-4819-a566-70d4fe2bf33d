/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardTransactionStatusEnum;
import top.continew.admin.biz.enums.CardTransactionTypeEnum;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 卡片交易流水详情信息
 *
 * <AUTHOR>
 * @since 2024/12/31 21:57
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "卡片交易流水详情信息")
public class CardTransactionDetailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台
     */
    @Schema(description = "平台")
    @ExcelProperty(value = "平台", converter = ExcelBaseEnumConverter.class)
    private CardPlatformEnum platform;

    /**
     * 卡号
     */
    @Schema(description = "卡号")
    @ExcelProperty(value = "卡号")
    private String cardNumber;

    @ExcelProperty(value = "广告户ID")
    private String adAccountId;

    /**
     * 交易编号
     */
    @Schema(description = "交易编号")
    @ExcelProperty(value = "交易编号")
    private String transactionId;

    /**
     * 交易类型
     */
    @Schema(description = "交易类型")
    @ExcelProperty(value = "交易类型", converter = ExcelBaseEnumConverter.class)
    private CardTransactionTypeEnum transType;

    /**
     * 交易状态
     */
    @Schema(description = "交易状态")
    @ExcelProperty(value = "交易状态", converter = ExcelBaseEnumConverter.class)
    private CardTransactionStatusEnum transStatus;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    @ExcelProperty(value = "交易金额")
    private BigDecimal transAmount;

    /**
     * 清算金额
     */
    @Schema(description = "清算金额")
    @ExcelProperty(value = "清算金额")
    private BigDecimal clearAmount;

    /**
     * 交易币种
     */
    @Schema(description = "交易币种")
    @ExcelProperty(value = "交易币种")
    private String transCurrency;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @ExcelProperty(value = "交易时间")
    private LocalDateTime transTime;

    @ExcelProperty(value = "中国时间")
    private LocalDateTime chinaTime;

    /**
     * 交易详情
     */
    @Schema(description = "交易详情")
    @ExcelProperty(value = "交易详情")
    private String transDetail;

    /**
     * 交易详情
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
}