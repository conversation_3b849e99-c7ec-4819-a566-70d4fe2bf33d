/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import com.alibaba.fastjson2.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import top.continew.admin.biz.enums.ClearOrderCardStatusEnum;
import top.continew.admin.biz.enums.ClearOrderStatusEnum;
import top.continew.admin.biz.enums.FbLimitCheckStatusEnum;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 清零订单信息
 *
 * <AUTHOR>
 * @since 2024/12/31 11:17
 */
@Data
@Schema(description = "清零订单信息")
public class ClearOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    private Long customerId;

    private String customerName;

    private String platformAdId;

    private String bmId;

    /**
     * 浏览器编号
     */
    private String browserNo;

    /**
     * 清零金额
     */
    @Schema(description = "清零金额")
    private BigDecimal clearAmount;

    /**
     * 卡片余额
     */
    @Schema(description = "卡片余额")
    private BigDecimal cardBalance;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private ClearOrderStatusEnum status;

    private ClearOrderCardStatusEnum cardStatus;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private Long handleUser;

    private String handleUserName;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime finishTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 凭证
     */
    @Schema(description = "凭证")
    private String certificate;

    /**
     * fb限额检测状态
     */
    private FbLimitCheckStatusEnum fbCheckStatus;

    /**
     * 卡台清零结果
     */
    private String cardClearResult;

    private List<AdAccountCardClearResultResp> cardClearResultList;

    public List<AdAccountCardClearResultResp> getCardClearResultList() {
        if (StringUtils.isBlank(cardClearResult)) {
            return new ArrayList<>();
        }
        return JSONArray.parseArray(cardClearResult, AdAccountCardClearResultResp.class);
    }
}