/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.CustomerSettleTypeEnum;
import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.service.AgentService;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.admin.common.constant.ContainerConstants;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户详情信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户详情信息")
public class CustomerDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @Assemble(prop = ":businessUserName", container = ContainerConstants.USER_NICKNAME)
    private Long businessUserId;

    @ExcelProperty(value = "关联商务")
    private String businessUserName;

    @AssembleMethod(props = @Mapping(src = "name", ref = "agentName"), targetType = AgentService.class, method = @ContainerMethod(bindMethod = "get", resultType = AgentResp.class))
    private Long agentId;

    @ExcelProperty(value = "关联中介")
    private String agentName;

    @ExcelProperty(value = "返点政策")
    private String rebateRule;

    /**
     * 手续费百分比
     */
    @Schema(description = "手续费百分比")
    @ExcelProperty(value = "服务费/%")
    private BigDecimal feeRatePercent;

    @ExcelProperty(value = "开户费")
    private BigDecimal buyAccountFee;
    /**
     * 余额
     */
    @Schema(description = "余额")
    @ExcelProperty(value = "余额")
    private BigDecimal balance;

    /**
     * TG群ID
     */
    @Schema(description = "TG群ID")
    @ExcelProperty(value = "TG群ID")
    private Long telegramChatId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    private Boolean isRefund;

    /**
     * 机器人权限
     */
    private String robotPermission;

    /**
     * 结算方式
     */
    private CustomerSettleTypeEnum settleType;

    /**
     * 结算限额
     */
    private BigDecimal settleLimitAmount;

    /**
     * 预警限额
     */
    private BigDecimal warnLimitAmount;

    /**
     * 上一次结算消耗
     */
    private BigDecimal lastSettleSpent;

    /**
     * 客户状态
     */
    @Schema(description = "客户状态")
    @ExcelProperty(value = "客户状态", converter = ExcelBaseEnumConverter.class)
    private CustomerStatusEnum status;

    /**
     * 终止时间
     */
    @Schema(description = "终止时间")
    @ExcelProperty(value = "终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime terminateTime;

    /**
     * 客户来源ID
     */
    @Schema(description = "客户来源ID")
    private Long sourceId;

    /**
     * 客户行业
     */
    @Schema(description = "客户行业")
    private Integer industry;

    /**
     * 客户类型：1是正式客户，2是潜在客户
     */
    private CustomerTypeEnum type;

    @Schema(description = "公司名称")
    private String companyName;
    @Schema(description = "客户职位")
    private String customerPosition;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "团队规模")
    private String teamSize;
    @Schema(description = "团队单日消耗(美元)")
    private String dailyTeamSpending;
    @Schema(description = "产品名称")
    private String productName;
}