/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CustomerInfoResp {

    @Schema(description = "总充值")
    private BigDecimal totalRecharge;
    @Schema(description = "总户数")
    private Integer totalAdAccount;
    @Schema(description = "死户数量")
    private Integer deadAdAccountNum;
    @Schema(description = "死户率")
    private BigDecimal deadProbabilities;
    @Schema(description = "当日充值")
    private BigDecimal toDayRecharge;
    @Schema(description = "昨日消耗")
    private BigDecimal yesterdaySpend;
    @Schema(description = "平均户消耗")
    private BigDecimal averageSpend;
    @Schema(description = "平均户充值")
    private BigDecimal averageRecharge;
    @Schema(description = "不活跃户")
    private List<String> inactiveAdAccount;

    @Schema(description = "总消耗")
    private BigDecimal totalSpend;
    @Schema(description = "卡台余额")
    private BigDecimal cardBalance;
}
