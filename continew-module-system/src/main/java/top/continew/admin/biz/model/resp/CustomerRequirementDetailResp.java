package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;
import java.util.List;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import cn.crane4j.annotation.condition.ConditionOnPropertyNotNull;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 客户需求详情信息
 *
 * <AUTHOR>
 * @since 2025/02/20 14:03
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户需求详情信息")
public class CustomerRequirementDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 时区
     */
    @Schema(description = "时区")
    @ExcelProperty(value = "时区")
    private String timezone;

    /**
     * 需求时间
     */
    @Schema(description = "需求时间")
    @ExcelProperty(value = "需求时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime requirementTime;

    /**
     * 需求数量
     */
    @Schema(description = "需求数量")
    @ExcelProperty(value = "需求数量")
    private Integer quantity;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ConditionOnPropertyNotNull
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "handleUserString"))
    private Long handleUser;

    private String handleUserString;

    private String cancelReason;



    /**
     * bm类型
     */
    private Integer bmType;

    /**
     * 客户BM ID
     */
    private String customerBmId;

    /**
     * 开户费
     */
    private BigDecimal payAmount;

    /**
     * 广告户名称
     */
    private String adAccountName;


    /**
     * 下户订单编号
     */
    private List<String> orderNos;
}