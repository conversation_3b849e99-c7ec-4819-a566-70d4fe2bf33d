/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;
import java.util.List;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.Mapping;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.CustomerSettleTypeEnum;
import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.enums.RechargeFeeHandleMethodEnum;
import top.continew.admin.biz.model.resp.crm.CustomerTagResp;
import top.continew.admin.biz.service.AgentService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.common.base.BaseResp;
import top.continew.admin.common.constant.ContainerConstants;

/**
 * 客户信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Data
@Schema(description = "客户信息")
public class CustomerResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @Assemble(prop = ":businessUserName", container = ContainerConstants.USER_NICKNAME)
    private Long businessUserId;

    private String businessUserName;

    @AssembleMethod(props = @Mapping(src = "name", ref = "agentName"), targetType = AgentService.class, method = @ContainerMethod(bindMethod = "get", resultType = AgentResp.class))
    private Long agentId;

    private String agentName;

    private String rebateRule;
    /**
     * 手续费百分比
     */
    @Schema(description = "手续费百分比")
    private BigDecimal feeRatePercent;

    /**
     * 手续费扣款方式
     */
    @Schema(description = "手续费扣款方式")
    private RechargeFeeHandleMethodEnum feeHandleMethod;

    /**
     * 余额
     */
    @Schema(description = "余额")
    private BigDecimal balance;

    /**
     * TG群ID
     */
    @Schema(description = "TG群ID")
    private Long telegramChatId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    private BigDecimal buyAccountFee;

    private Boolean isRefund;

    /**
     * 结算方式
     */
    private CustomerSettleTypeEnum settleType;

    /**
     * 结算限额
     */
    private BigDecimal settleLimitAmount;

    /**
     * 预警限额
     */
    private BigDecimal warnLimitAmount;

    /**
     * 上一次结算消耗
     */
    private BigDecimal lastSettleSpent;
    
    /**
     * 客户状态
     */
    @Schema(description = "客户状态")
    private CustomerStatusEnum status;


    /**
     * 客户类型：1是正式客户，2是潜在客户
     */
    private CustomerTypeEnum type;


    /**
     * 客户来源ID
     */
    private Long sourceId;

    /**
     * 客户行业
     */
    private Integer industry;
    
    /**
     * 终止时间
     */
    @Schema(description = "终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime terminateTime;


    @Schema(description = "公司名称")
    private String companyName;
    @Schema(description = "客户职位")
    private String customerPosition;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "团队规模")
    private String teamSize;
    @Schema(description = "团队单日消耗(美元)")
    private String dailyTeamSpending;
    @Schema(description = "产品名称")
    private String productName;

    private String username;

    private LocalDateTime cooperateTime;

}