package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 客户钱包信息
 *
 * <AUTHOR>
 * @since 2025/07/22 16:58
 */
@Data
@Schema(description = "客户钱包信息")
public class CustomerWalletResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 钱包地址
     */
    @Schema(description = "钱包地址")
    private String walletAddress;
}