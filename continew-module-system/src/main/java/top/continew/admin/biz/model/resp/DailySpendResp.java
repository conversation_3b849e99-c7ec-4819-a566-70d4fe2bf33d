package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "每日消耗统计响应")
public class DailySpendResp {
    
    @Schema(description = "交易时间")
    private LocalDate tradeTime;
    
    @Schema(description = "总消耗")
    private BigDecimal totalSpend;

    @Schema(description = "利润")
    private BigDecimal profit;
}