/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * IP库信息
 *
 * <AUTHOR>
 * @since 2025/01/20 13:46
 */
@Data
@Schema(description = "IP库信息")
public class IpResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ip地址
     */
    @Schema(description = "ip地址")
    private String host;

    /**
     * 端口
     */
    @Schema(description = "端口")
    private String port;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;

    /**
     * 使用次数
     */
    @Schema(description = "使用次数")
    private Integer useTimes;

    /**
     * 成功次数
     */
    @Schema(description = "成功次数")
    private Integer successTimes;

    private String country;

    private LocalDateTime expireTime;
}