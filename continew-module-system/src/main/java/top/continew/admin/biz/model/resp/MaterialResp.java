package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.AssembleMethod;
import cn.crane4j.annotation.ContainerMethod;
import cn.crane4j.annotation.MappingType;
import cn.crane4j.core.executor.handler.ManyToManyAssembleOperationHandler;
import cn.crane4j.core.executor.handler.key.ReflectiveAliasPropertyKeyResolver;
import cn.crane4j.core.executor.handler.key.ReflectiveSeparablePropertyKeyResolver;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.MaterialTypeEnum;
import top.continew.admin.biz.service.MaterialService;
import top.continew.admin.common.base.BaseResp;
import top.continew.admin.common.constant.ContainerConstants;

/**
 * 物料信息
 *
 * <AUTHOR>
 * @since 2025/01/21 16:20
 */
@Data
@Schema(description = "物料信息")
@Assemble(keyType = MaterialResp.class, keyDesc = "payPrice,num", prop = ":unitPrice", container = ContainerConstants.MATERIAL_UNIT_PRICE_CAL)
public class MaterialResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private LocalDateTime payDate;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private MaterialTypeEnum type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer num;

    /**
     * 渠道
     */
    @Schema(description = "渠道")
    private String channel;

    private Long channelId;

    private String channelName;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额")
    private BigDecimal payPrice;

    @Schema(description = "单价")
    private BigDecimal unitPrice;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}