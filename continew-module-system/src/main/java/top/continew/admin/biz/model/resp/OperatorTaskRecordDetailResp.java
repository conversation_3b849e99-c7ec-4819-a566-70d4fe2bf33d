package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 运营人员工作记录详情信息
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "运营人员工作记录详情信息")
public class OperatorTaskRecordDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 广告户
     */
    @Schema(description = "广告户")
    @ExcelProperty(value = "广告户")
    private String platformAdId;

    /**
     * 类型
     */
    @Schema(description = "类型")
    @ExcelProperty(value = "类型")
    private Integer type;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @ExcelProperty(value = "数量")
    private Integer num;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
}