/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.FbLimitCheckStatusEnum;
import top.continew.admin.biz.enums.RechargeOrderCardStatusEnum;
import top.continew.admin.biz.enums.RechargeOrderStatusEnum;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值订单信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:09
 */
@Data
@Schema(description = "充值订单信息")
public class RechargeOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    private Long customerId;

    private String customerName;

    private BigDecimal customerBalance;

    private String platformAdId;

    private String bmId;

    private BigDecimal spendCap;

    /**
     * 广告户浏览器编号
     */
    private String browserNo;
    /**
     * 充值金额
     */
    @Schema(description = "充值金额")
    private BigDecimal amount;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private RechargeOrderStatusEnum status;

    /**
     * 卡片充值状态
     */
    private RechargeOrderCardStatusEnum cardStatus;

    /**
     * fb限额检测状态
     */
    private FbLimitCheckStatusEnum fbCheckStatus;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private Long handleUser;

    private String handleUserName;

    /**
     * 接收时间
     */
    @Schema(description = "接收时间")
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime finishTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 凭证
     */
    private String certificate;

    /**
     * 广告户充值金额
     */
    private BigDecimal rechargeAmount;
}