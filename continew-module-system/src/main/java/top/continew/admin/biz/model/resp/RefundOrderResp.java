/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.FbLimitCheckStatusEnum;
import top.continew.admin.biz.enums.RefundOrderCardStatusEnum;
import top.continew.admin.biz.enums.RefundOrderStatusEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 退款订单信息
 *
 * <AUTHOR>
 * @since 2025/01/10 15:39
 */
@Data
@Schema(description = "退款订单信息")
public class RefundOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 客户
     */
    @Schema(description = "客户")
    private Long customerId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    private String platformAdId;

    /**
     * 卡台状态
     */
    @Schema(description = "卡台状态")
    private RefundOrderCardStatusEnum cardStatus;

    /**
     * fb检测状态
     */
    @Schema(description = "fb检测状态")
    private FbLimitCheckStatusEnum fbCheckStatus;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额")
    private BigDecimal amount;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private RefundOrderStatusEnum status;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private Long handleUser;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    private LocalDateTime handleTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private LocalDateTime finishTime;

    /**
     * 凭证
     */
    @Schema(description = "凭证")
    private String certificate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    private String customerName;

    private String bmId;

    private String browserNo;

    private String handleUserName;
}