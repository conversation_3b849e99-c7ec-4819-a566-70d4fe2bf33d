package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 商务日报详情信息
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "商务日报详情信息")
public class SalesDailySummaryDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日报记录的日期
     */
    @Schema(description = "日报记录的日期")
    @ExcelProperty(value = "日报记录的日期")
    private LocalDate recordDate;

    /**
     * 日报具体内容，支持长文本
     */
    @Schema(description = "日报具体内容，支持长文本")
    @ExcelProperty(value = "日报具体内容，支持长文本")
    private String content;
}