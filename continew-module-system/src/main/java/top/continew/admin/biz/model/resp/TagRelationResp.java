package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 标签关联信息
 *
 * <AUTHOR>
 * @since 2025/05/08 16:37
 */
@Data
@Schema(description = "标签关联信息")
public class TagRelationResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    @Schema(description = "标签ID")
    private Long tagId;

    /**
     * 类型(1=广告户 2=下户订单)
     */
    @Schema(description = "类型(1=广告户 2=下户订单)")
    private Integer type;

    /**
     * 关联ID
     */
    @Schema(description = "关联ID")
    private Long relationId;
}