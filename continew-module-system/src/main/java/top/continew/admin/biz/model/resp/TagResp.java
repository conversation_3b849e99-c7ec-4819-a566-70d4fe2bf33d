package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 标签信息
 *
 * <AUTHOR>
 * @since 2025/05/07 18:02
 */
@Data
@Schema(description = "标签信息")
public class TagResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private Long createName;
}