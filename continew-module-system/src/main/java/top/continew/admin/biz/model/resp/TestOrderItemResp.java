package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 测试任务详情信息
 *
 * <AUTHOR>
 * @since 2025/05/13 11:43
 */
@Data
@Schema(description = "测试任务详情信息")
public class TestOrderItemResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联测试任务
     */
    @Schema(description = "关联测试任务")
    private Long orderId;

    /**
     * 广告户ID
     */
    @Schema(description = "广告户ID")
    private String platformAdId;

    /**
     * 状态（1=待进行，2=进行中，3=测试通过，4=测试失败）
     */
    @Schema(description = "状态（1=待进行，2=进行中，3=测试通过，4=测试失败）")
    private Integer status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}