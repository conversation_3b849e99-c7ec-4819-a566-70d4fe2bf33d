package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.AdAccountUnusableReasonEnum;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ExcelIgnoreUnannotated
public class UnSpentOrderResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    @ExcelProperty(value = "关联客户")
    private String customerName;

    @ExcelProperty(value = "账号类型", converter = ExcelBaseEnumConverter.class)
    private BusinessManagerTypeEnum bmType;

    /**
     * 关联广告户
     */
    @Schema(description = "关联广告户")
    @ExcelProperty(value = "关联广告户")
    private String adAccountId;

    /**
     * 广告户主键
     */
    private Long adAccountPrimaryKey;

    @ExcelProperty(value = "面板消耗")
    private BigDecimal amountSpent;

    @ExcelProperty(value = "总消耗")
    private BigDecimal totalSpent;

    @ExcelProperty(value = "卡台消耗")
    private BigDecimal cardSpent;

    /**
     * 时区
     */
    @Schema(description = "时区")
    @ExcelProperty(value = "时区")
    private String timezone;

    @Schema(description = "清零状态")
    @ExcelProperty(value = "清零状态", converter = ExcelBaseEnumConverter.class)
    private AdAccountClearStatusEnum clearStatus;

    @ExcelProperty(value = "清零时间")
    private LocalDateTime clearTime;

    @ExcelProperty(value = "下户时间")
    private LocalDateTime finishTime;


    @ExcelProperty(value = "BM授权时间")
    private LocalDateTime bmAuthTime;

    private String browserNo;

    @ExcelProperty(value = "开启广告系列时间")
    private LocalDateTime startCampaignTime;

    private String campaignDesc;

    private BigDecimal rechargeAmount;

    private LocalDateTime rechargeTime;

    private BigDecimal spentAmount;

    private LocalDate spentTime;

    private Long customerId;

}
