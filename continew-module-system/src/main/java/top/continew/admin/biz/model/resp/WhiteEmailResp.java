package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 白名单邮箱信息
 *
 * <AUTHOR>
 * @since 2025/07/01 14:36
 */
@Data
@Schema(description = "白名单邮箱信息")
public class WhiteEmailResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;
}