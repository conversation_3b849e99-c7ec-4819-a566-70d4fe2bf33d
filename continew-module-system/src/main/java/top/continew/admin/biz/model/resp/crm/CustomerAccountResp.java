package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 客户账号信息信息
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "客户账号信息信息")
public class CustomerAccountResp {


    @Schema(description = "关联ID")
    private Long relId;


    @Schema(description = "实体类型：1-线索、2-商机、3-客户")
    private Integer entityType;

    @Schema(description = "实体ID")
    private Long entityId;

    @Schema(description = "账号ID")
    private Long accountId;

    @Schema(description = "账号类型：对应着社交账号类型")
    private Integer accountType;

    @Schema(description = "账号")
    private String account;

    @Schema(description = "关联的社交账号ID")
    private Long socialAccountId;

    @Schema(description = "关联的社交账号")
    private String socialAccount;

}