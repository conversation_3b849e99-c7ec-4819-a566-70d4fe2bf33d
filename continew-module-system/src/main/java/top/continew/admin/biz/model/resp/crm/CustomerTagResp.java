package top.continew.admin.biz.model.resp.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 客户标签响应
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "客户标签响应")
public class CustomerTagResp {
    
    /**
     * 标签ID
     */
    @Schema(description = "标签ID")
    private Long tagId;
    
    /**
     * 标签名称
     */
    @Schema(description = "标签名称")
    private String tagName;
    
    /**
     * 标签类型编码
     */
    @Schema(description = "标签类型编码")
    private String code;

}