package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 客户回访策略详情信息
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "客户回访策略详情信息")
public class CustomerVisitStrategyDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 策略名称
     */
    @Schema(description = "策略名称")
    @ExcelProperty(value = "策略名称")
    private String strategyName;

    /**
     * 策略描述
     */
    @Schema(description = "策略描述")
    @ExcelProperty(value = "策略描述")
    private String strategyDesc;

    /**
     * 适用客户类型：1-潜在客户，2-正式客户
     */
    @Schema(description = "适用客户类型：1-潜在客户，2-正式客户")
    @ExcelProperty(value = "适用客户类型：1-潜在客户，2-正式客户")
    private Integer customerType;

    /**
     * 策略状态：0-禁用，1-启用
     */
    @Schema(description = "策略状态：0-禁用，1-启用")
    @ExcelProperty(value = "策略状态：0-禁用，1-启用")
    private Integer strategyStatus;

    /**
     * 策略条件配置(JSON格式)
     */
    @Schema(description = "策略条件配置(JSON格式)")
    @ExcelProperty(value = "策略条件配置(JSON格式)")
    private String strategyConditions;

    /**
     * 上次扫描时间
     */
    @Schema(description = "上次扫描时间")
    @ExcelProperty(value = "上次扫描时间")
    private LocalDateTime lastScanTime;

    /**
     * 上次扫描符合条件的客户数
     */
    @Schema(description = "上次扫描符合条件的客户数")
    @ExcelProperty(value = "上次扫描符合条件的客户数")
    private Integer lastScanMatchCount;
}