package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 客户回访策略信息
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
@Data
@Schema(description = "客户回访策略信息")
public class CustomerVisitStrategyResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 策略名称
     */
    @Schema(description = "策略名称")
    private String strategyName;

    /**
     * 策略描述
     */
    @Schema(description = "策略描述")
    private String strategyDesc;

    /**
     * 适用客户类型：1-潜在客户，2-正式客户
     */
    @Schema(description = "适用客户类型：1-潜在客户，2-正式客户")
    private Integer customerType;

    /**
     * 策略状态：0-禁用，1-启用
     */
    @Schema(description = "策略状态：0-禁用，1-启用")
    private Integer strategyStatus;

    /**
     * 策略条件配置(JSON格式)
     */
    @Schema(description = "策略条件配置(JSON格式)")
    private String strategyConditions;

    /**
     * 上次扫描时间
     */
    @Schema(description = "上次扫描时间")
    private LocalDateTime lastScanTime;

    /**
     * 上次扫描符合条件的客户数
     */
    @Schema(description = "上次扫描符合条件的客户数")
    private Integer lastScanMatchCount;
}