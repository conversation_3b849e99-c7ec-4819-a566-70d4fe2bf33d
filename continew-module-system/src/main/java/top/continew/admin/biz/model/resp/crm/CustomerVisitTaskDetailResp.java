package top.continew.admin.biz.model.resp.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.VisitTaskStatusEnum;
import top.continew.admin.biz.model.resp.CustomerDetailResp;

import java.time.LocalDateTime;

/**
 * 客户回访任务响应信息
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
@Schema(description = "客户回访任务响应信息")
public class CustomerVisitTaskDetailResp {


    /**
     * 客户ID
     */
    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户信息")
    private CustomerDetailResp customer;

    /**
     * 触发策略ID
     */
    @Schema(description = "触发策略ID")
    private Long strategyId;

    /**
     * 触发策略名称
     */
    @Schema(description = "触发策略名称")
    private String strategyName;

    /**
     * 任务状态：1-待处理，2-处理中，3-已完成，4-已关闭
     */
    @Schema(description = "任务状态：1-待处理，2-处理中，3-已完成，4-已关闭")
    private VisitTaskStatusEnum taskStatus;

    /**
     * 负责人ID
     */
    @Schema(description = "负责人ID")
    private Long assigneeId;

    @Schema(description = "名称人名称")
    private String assigneeUserName;
    /**
     * 要求完成时间
     */
    @Schema(description = "要求完成时间")
    private LocalDateTime requiredFinishTime;

    /**
     * 回访提醒时间
     */
    private LocalDateTime reminderTime;

    /**
     * 回访目标
     */
    private String visitGoal;

    /**
     * 回访结果：1-达成目标，2-无效沟通，3-客户拒绝，4-未达成目标
     */
    private Integer visitResult;

    /**
     * 关闭原因
     */
    @Schema(description = "关闭原因")
    private String closeReason;

    /**
     * 附件URL，多个用逗号分隔
     */
    @Schema(description = "附件URL，多个用逗号分隔")
    private String attachmentUrls;

    /**
     * 触发条件信息，记录客户满足的具体条件
     */
    @Schema(description = "触发条件信息，记录客户满足的具体条件")
    private String triggerInfo;


}