package top.continew.admin.biz.model.resp.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户回访任务明细响应信息
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Data
@Schema(description = "客户回访任务明细响应信息")
public class CustomerVisitTaskDetailsResp {

    /**
     * ID
     */
    @Schema(description = "ID", example = "1")
    private String id;

    /**
     * 回访任务ID
     */
    @Schema(description = "回访任务ID", example = "1")
    private String taskId;

    /**
     * 回访方式
     */
    @Schema(description = "回访方式", example = "电话")
    private String visitMethod;

    /**
     * 回访时间
     */
    @Schema(description = "回访时间")
    private LocalDateTime visitTime;

    /**
     * 回访纪要
     */
    @Schema(description = "回访纪要")
    private String visitSummary;

    /**
     * 附件URL
     */
    @Schema(description = "附件URL")
    private String attachmentUrls;


}