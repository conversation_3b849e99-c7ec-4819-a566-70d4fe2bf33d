package top.continew.admin.biz.model.resp.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索跟进记录响应
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "线索跟进记录响应")
public class LeadFollowResp {

    /**
     * 跟进记录ID
     */
    @Schema(description = "跟进记录ID")
    private Long id;

    /**
     * 线索ID
     */
    @Schema(description = "线索ID")
    private Long leadId;

    /**
     * 跟进人ID
     */
    @Schema(description = "跟进人ID")
    private Long followUserId;

    /**
     * 跟进人名称
     */
    @Schema(description = "跟进人名称")
    private String followUserName;

    /**
     * 跟进内容
     */
    @Schema(description = "跟进内容")
    private String content;

    /**
     * 附件地址
     */
    @Schema(description = "附件地址")
    private String attachment;

    /**
     * 跟进时间
     */
    @Schema(description = "跟进时间")
    private LocalDateTime followTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}