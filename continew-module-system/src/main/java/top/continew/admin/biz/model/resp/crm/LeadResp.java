package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;
import java.util.List;

import cn.crane4j.annotation.Assemble;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.LeadStatusEnum;
import top.continew.admin.common.base.BaseResp;
import top.continew.admin.common.constant.ContainerConstants;

/**
 * 线索信息
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "线索信息")
public class LeadResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-创建商机、5-无效
     */
    @Schema(description = "状态")
    private LeadStatusEnum status;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 客户行业
     */
    @Schema(description = "客户行业")
    private Integer customerIndustry;

    /**
     * 需求内容
     */
    @Schema(description = "需求内容")
    private String requirement;

    /**
     * 提醒时间
     */
    @Schema(description = "提醒时间")
    private LocalDateTime remindTime;

    /**
     * 无效原因
     */
    @Schema(description = "无效原因")
    private Integer invalidReason;

    /**
     * 关联商机ID
     */
    @Schema(description = "关联商机ID")
    private Long opportunityId;

    /**
     * 最近跟进时间
     */
    @Schema(description = "最近跟进时间")
    private LocalDateTime lastFollowTime;

    /**
     * 对接人
     */
    @Schema(description = "对接人")
    @Assemble(prop = ":handlerUserName", container = ContainerConstants.USER_NICKNAME)
    private Long handlerUserId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    @Schema(description = "对接人名称")
    private String handlerUserName;



    @Schema(description = "最近跟进记录")
    private LeadFollowResp lastFollow;


    @Schema(description = "公司名称")
    private String companyName;
    @Schema(description = "客户职位")
    private String customerPosition;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "团队规模")
    private String teamSize;
    @Schema(description = "团队单日消耗(美元)")
    private String dailyTeamSpending;
    @Schema(description = "产品名称")
    private String productName;

}