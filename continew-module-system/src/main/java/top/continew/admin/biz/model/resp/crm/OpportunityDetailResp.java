package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;
import java.util.List;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.OpportunityStatusEnum;
import top.continew.admin.biz.model.resp.CustomerDetailResp;
import top.continew.admin.common.base.BaseDetailResp;

/**
 * 商机详情信息
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "商机详情信息")
public class OpportunityDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户ID
     */
    @Schema(description = "关联客户ID")
    @ExcelProperty(value = "关联客户ID")
    private Long customerId;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    @ExcelProperty(value = "来源ID")
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失
     */
    @Schema(description = "状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失")
    @ExcelProperty(value = "状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失")
    private OpportunityStatusEnum status;

    /**
     * 需求内容
     */
    @Schema(description = "需求内容")
    @ExcelProperty(value = "需求内容")
    private String requirement;

    /**
     * 提醒时间
     */
    @Schema(description = "提醒时间")
    @ExcelProperty(value = "提醒时间")
    private LocalDateTime remindTime;

    /**
     * 流失原因
     */
    @Schema(description = "流失原因")
    @ExcelProperty(value = "流失原因")
    private Integer lostReason;

    /**
     * 最近跟进时间
     */
    @Schema(description = "最近跟进时间")
    @ExcelProperty(value = "最近跟进时间")
    private LocalDateTime lastFollowTime;

    /**
     * 对接人
     */
    @Schema(description = "对接人")
    @ExcelProperty(value = "对接人")
    private Long handlerUserId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @Schema(description = "客户账号信息")
    private List<CustomerAccountResp> accounts;

    @Schema(description = "客户信息")
    private CustomerDetailResp customer;

    @Schema(description = "公司名称")
    private String companyName;
    @Schema(description = "客户职位")
    private String customerPosition;
    @Schema(description = "城市")
    private String city;
    @Schema(description = "团队规模")
    private String teamSize;
    @Schema(description = "团队单日消耗(美元)")
    private String dailyTeamSpending;
    @Schema(description = "产品名称")
    private String productName;
}