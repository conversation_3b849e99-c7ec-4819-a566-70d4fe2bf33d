package top.continew.admin.biz.model.resp.crm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商机跟进记录响应
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "商机跟进记录响应")
public class OpportunityFollowResp {

    @Schema(description = "跟进记录ID")
    private Long id;

    @Schema(description = "跟进内容")
    private String content;

    @Schema(description = "附件")
    private String attachment;

    @Schema(description = "跟进时间")
    private LocalDateTime followTime;

    @Schema(description = "跟进人ID")
    private Long followUserId;

    @Schema(description = "跟进人姓名")
    private String followUserName;
}