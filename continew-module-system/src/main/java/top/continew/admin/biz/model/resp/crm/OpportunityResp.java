package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.OpportunityStatusEnum;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.resp.CustomerDetailResp;
import top.continew.admin.biz.model.resp.CustomerResp;
import top.continew.admin.common.base.BaseResp;
import top.continew.admin.common.constant.ContainerConstants;

/**
 * 商机信息
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
@Data
@Schema(description = "商机信息")
public class OpportunityResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户ID
     */
    @Schema(description = "关联客户ID")
    private Long customerId;

    /**
     * 来源ID
     */
    @Schema(description = "来源ID")
    private Long sourceId;

    /**
     * 状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失
     */
    @Schema(description = "状态：1-待跟进、2-跟进中、3-长期跟进、4-赢单、5-流失")
    private OpportunityStatusEnum status;

    /**
     * 需求内容
     */
    @Schema(description = "需求内容")
    private String requirement;

    /**
     * 提醒时间
     */
    @Schema(description = "提醒时间")
    private LocalDateTime remindTime;

    /**
     * 流失原因
     */
    @Schema(description = "流失原因")
    private Integer lostReason;

    /**
     * 最近跟进时间
     */
    @Schema(description = "最近跟进时间")
    private LocalDateTime lastFollowTime;

    /**
     * 对接人
     */
    @Schema(description = "对接人")
    @Assemble(prop = ":handlerUserName", container = ContainerConstants.USER_NICKNAME)
    private Long handlerUserId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


    @Schema(description = "对接人名称")
    private String handlerUserName;

    @Schema(description = "客户信息")
    private CustomerDetailResp customer;

    @Schema(description = "最后跟进信息")
    private OpportunityFollowResp lastFollow;

}