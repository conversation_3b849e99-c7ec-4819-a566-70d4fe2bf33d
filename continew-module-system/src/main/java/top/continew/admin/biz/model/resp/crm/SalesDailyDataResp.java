package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 商务每日数据信息
 *
 * <AUTHOR>
 * @since 2025/07/11 14:30
 */
@Data
@Schema(description = "商务每日数据信息")
public class SalesDailyDataResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 线索ID
     */
    @Schema(description = "线索ID")
    private Long leadId;

    /**
     * 记录添加日期，默认为当前日期
     */
    @Schema(description = "记录添加日期，默认为当前日期")
    private LocalDate recordDate;

    /**
     * 添加方式，例如：被动，主动
     */
    @Schema(description = "添加方式，例如：被动，主动")
    private Integer addMethod;

    /**
     * 客户账号类型，例如：微信，tg
     */
    @Schema(description = "客户账号类型，例如：微信，tg")
    private Integer accountType;

    /**
     * 客户的微信号或tg id
     */
    @Schema(description = "客户的微信号或tg id")
    private String customerAccount;

    private String customerName;

    private Long socialAccountId;

    private String socialAccountName;

    /**
     * 客户的大致业务范围或描述
     */
    @Schema(description = "客户的大致业务范围或描述")
    private String customerBusiness;

    /**
     * 客户所在的城市
     */
    @Schema(description = "客户所在的城市")
    private String customerCity;

    /**
     * 对客户的初步情况进行描述
     */
    @Schema(description = "对客户的初步情况进行描述")
    private String customerOverview;

    /**
     * 记录与客户首次沟通的内容和结果
     */
    @Schema(description = "记录与客户首次沟通的内容和结果")
    private String firstContactNotes;

    /**
     * 创建人
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createUserId;

    /**
     * 创建人
     */
    @Schema(description = "创建人", example = "超级管理员")
    @ExcelProperty(value = "创建人")
    private String createUserString;
}