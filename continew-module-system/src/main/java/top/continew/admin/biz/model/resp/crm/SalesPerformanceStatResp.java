package top.continew.admin.biz.model.resp.crm;

import lombok.Data;

/**
 * 商务业绩统计响应
 *
 * <AUTHOR>
 * @since 2025/01/16
 */
@Data
public class SalesPerformanceStatResp {

    /**
     * 商务人员ID
     */
    private Long salesUserId;

    /**
     * 商务人员姓名
     */
    private String salesUserName;

    /**
     * 客咨总数
     */
    private Long totalCustomerConsultations;

    /**
     * 微信客咨数
     */
    private Long wechatConsultations;

    /**
     * TG客咨数
     */
    private Long telegramConsultations;

    /**
     * 其他账号类型客咨数
     */
    private Long otherConsultations;

    /**
     * 线索总数
     */
    private Long totalLeads;

    /**
     * 跟进中线索数（包括跟进中、长期跟进）
     */
    private Long followingLeads;

    /**
     * 创建商机线索数
     */
    private Long opportunityLeads;

    /**
     * 无效线索数
     */
    private Long invalidLeads;

    /**
     * 商机总数
     */
    private Long totalOpportunities;

    /**
     * 跟进中商机数（包括跟进中、长期跟进）
     */
    private Long followingOpportunities;

    /**
     * 赢单商机数
     */
    private Long wonOpportunities;

    /**
     * 流失商机数
     */
    private Long lostOpportunities;

    /**
     * 成交客户数（正式客户）
     */
    private Long formalCustomers;

    private Long totalVisits;

    private Long pendingVisits;
    private Long processingVisits;
    private Long completedVisits;


}