package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 社交账号详情信息
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "社交账号详情信息")
public class SocialAccountDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账号类型：telegram、wechat、line、phone
     */
    @Schema(description = "账号类型")
    @ExcelProperty(value = "账号类型")
    private Integer accountType;

    /**
     * 账号
     */
    @Schema(description = "账号")
    @ExcelProperty(value = "账号")
    private String account;



    /**
     * 分配人
     */
    @Schema(description = "分配人")
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "assigneeName"))
    private Long assigneeId;

    /**
     * 分配人名称
     */
    private String assigneeName;

    /**
     * 账号状态：1-启用、2-禁用
     */
    @Schema(description = "账号状态：1-启用、2-禁用")
    @ExcelProperty(value = "账号状态：1-启用、2-禁用")
    private Integer status;
}