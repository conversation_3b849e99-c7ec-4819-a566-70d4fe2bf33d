package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import cn.crane4j.annotation.Assemble;
import cn.crane4j.annotation.Mapping;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;
import top.continew.starter.extension.crud.constant.ContainerPool;

/**
 * 社交账号信息
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@Schema(description = "社交账号信息")
public class SocialAccountResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账号类型：telegram、wechat、line、phone
     */
    @Schema(description = "账号类型：telegram、wechat、line、phone")
    private Integer accountType;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String account;

    /**
     * 分配人
     */
    @Schema(description = "分配人")
    @Assemble(container = ContainerPool.USER_NICKNAME, props = @Mapping(ref = "assigneeName"))
    private Long assigneeId;

    /**
     * 分配人名称
     */
    private String assigneeName;

    /**
     * 账号状态：1-启用、2-禁用
     */
    @Schema(description = "账号状态：1-启用、2-禁用")
    private Integer status;
}