package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.common.base.BaseDetailResp;

/**
 * 来源详情信息
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "来源详情信息")
public class SourceDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源分组ID
     */
    @Schema(description = "来源分组ID")
    @ExcelProperty(value = "来源分组ID")
    private Long groupId;

    /**
     * 来源分组名称
     */
    private String groupName;

    /**
     * 来源名称
     */
    @Schema(description = "来源名称")
    @ExcelProperty(value = "来源名称")
    private String name;
}