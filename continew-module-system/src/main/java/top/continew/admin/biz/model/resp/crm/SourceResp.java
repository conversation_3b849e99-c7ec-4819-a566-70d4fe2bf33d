package top.continew.admin.biz.model.resp.crm;

import java.io.Serial;
import java.time.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.common.base.BaseResp;

/**
 * 来源信息
 *
 * <AUTHOR>
 * @since 2025/05/16 17:23
 */
@Data
@Schema(description = "来源信息")
public class SourceResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源分组ID
     */
    @Schema(description = "来源分组ID")
    private Long groupId;

    /**
     * 来源名称
     */
    @Schema(description = "来源名称")
    private String name;

    /**
     * 来源分组名称
     */
    private String groupName;
}