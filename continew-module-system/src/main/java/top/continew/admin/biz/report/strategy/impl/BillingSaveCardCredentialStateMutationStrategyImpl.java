package top.continew.admin.biz.report.strategy.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.ReportType;
import top.continew.admin.biz.event.AdAccountBindCardEvent;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.system.model.entity.UserDO;

@Service
@RequiredArgsConstructor
public class BillingSaveCardCredentialStateMutationStrategyImpl implements ReportOpsStrategy {

    private final CardService cardService;

    @Override
    public ReportType getReport() {
        return ReportType.BILLING_SAVE_CARD_CREDENTIAL_STATE_MUTATION;
    }

    @Override
    public String handle(BrowserReq browserReq, UserDO userDO) {
        JSONObject data = JSONObject.from(browserReq.getData());
        JSONObject cardInfo = data.getJSONObject("cardInfo");
        if (cardInfo == null) {
            return "";
        }
        JSONObject secretPayload = cardInfo.getJSONObject("secretPayload");
        if (secretPayload == null) {
            return "";
        }
        String cardNumber = secretPayload.getString("credit_card");
        String platformAdId = cardInfo.getString("paymentAccountID");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cardNumber", cardNumber);
        jsonObject.put("platformAdId", platformAdId);
        CardDO card = cardService.getByCardNumber(cardNumber, true);
        if (card != null) {
            jsonObject.put("cardPlatform", card.getPlatform());
            AdAccountBindCardEvent.AdAccountBindCardModel adAccountBindCardModel = new AdAccountBindCardEvent.AdAccountBindCardModel(cardNumber, platformAdId, card.getPlatform());
            SpringUtil.publishEvent(new AdAccountBindCardEvent(adAccountBindCardModel));
        }
        return jsonObject.toString();
    }
}
