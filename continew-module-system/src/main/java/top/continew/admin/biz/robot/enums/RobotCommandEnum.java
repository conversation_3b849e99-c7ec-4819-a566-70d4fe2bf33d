/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import top.continew.starter.core.enums.BaseEnum;

@Getter
@RequiredArgsConstructor
public enum RobotCommandEnum implements BaseEnum<Integer> {
    RECHARGE_TEMP(1, "广告户充值"), RECHARGE(2, "【广告户充值】"), CLEAR_TEMP(3, "广告户清零"), CLEAR(4, "【广告户清零】"),
    REFUND_TEMP(5, "广告户减款"), REFUND(6, "【广告户减款】"), APPEAL_TEMP(7, "广告户申诉"), APPEAL(8, "【广告户申诉】"),
    CUSTOMER_RECHARGE_TEMP(9, "客户打款"), CUSTOMER_RECHARGE_PREVIEW(10, "【客户打款】"),
    CUSTOMER_RECHARGE_CONFIRM(11, "确认打款"), PROCESS_OF_AD_ACCOUNT_ORDER_TEMP(12, "下户进度"),
    PROCESS_OF_AD_ACCOUNT_ORDER(13, "【下户进度】"), UPLOAD_CERTIFICATE(14, "打款水单上传"),
    BUSINESS_DAILY(50,"【商务日报】"),
    CARD_PLATFORM_QUERY(80, "卡台查询"), POSTPAY_CUSTOMER_BALANCE(81, "实消客户余额"),
    EXTERNAL_AD_ACCOUNT_INVENTORY(94, "外部号库存"), AD_ACCOUNT_KEEP_SUCCESS_INVENTORY(95, "大黑号库存"),
    AD_ACCOUNT_INVENTORY_NOW(96, "广告户实时库存"), AD_ACCOUNT_INVENTORY(97, "广告户库存"), GET_CHAT_ID(98, "群ID"),
    UNKNOWN(99, "未知");

    private final Integer value;
    private final String description;
}
