package top.continew.admin.biz.robot.strategy;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.core.validation.CheckUtils;

@Service
public class BaseRobotCommandService {

    @Resource
    protected CustomerService customerService;

    @Resource
    protected TelegramChatIdConfig telegramChatIdConfig;

    public CustomerDO getCustomer(String customerName, Long chatId) {
        CustomerDO customer;
        if (StringUtils.isNotBlank(customerName)) {
            customer = customerService.getOne(Wrappers.<CustomerDO>lambdaQuery().eq(CustomerDO::getName, customerName));
        } else {
            customer = customerService.getByTelegramChatId(String.valueOf(chatId));
        }
        CheckUtils.throwIfNull(customer, "未找到相关客户");
        return customer;
    }

    public boolean hasPermission(RobotCommandEnum robotCommand, Long chatId) {
        if (chatId.equals(telegramChatIdConfig.getRechargeChatId())) {
            return true;
        }
        CustomerDO customer = customerService.getByTelegramChatId(String.valueOf(chatId));
        if (customer == null || StringUtils.isBlank(customer.getRobotPermission())) {
            return false;
        }
        switch (robotCommand) {
            case RECHARGE_TEMP, RECHARGE -> {
                return customer.getRobotPermission().contains(RobotCommandEnum.RECHARGE_TEMP.getDescription());
            }
            case CLEAR_TEMP, CLEAR -> {
                return customer.getRobotPermission().contains(RobotCommandEnum.CLEAR_TEMP.getDescription());
            }
            case REFUND_TEMP, REFUND -> {
                return customer.getRobotPermission().contains(RobotCommandEnum.REFUND_TEMP.getDescription());
            }
            case APPEAL_TEMP, APPEAL -> {
                return customer.getRobotPermission().contains(RobotCommandEnum.APPEAL_TEMP.getDescription());
            }
        }
        return false;
    }
}
