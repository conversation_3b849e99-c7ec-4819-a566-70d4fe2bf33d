package top.continew.admin.biz.robot.strategy.impl;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.model.entity.SalesDailySummaryDO;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.SalesDailySummaryService;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessDailyStrategyImpl implements RobotCommandStrategy {

    private final SalesDailySummaryService salesDailySummaryService;

    private final UserService userService;

    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.BUSINESS_DAILY;
    }

    @Override
    public String execute(Update update) {



        // 登录系统用户
        String userName = update.getMessage().getFrom().getUserName();

        UserDO user = userService.getBaseMapper()
                .selectOne(new LambdaQueryWrapper<UserDO>()
                        .eq(UserDO::getTelegramId, userName));

        if (user == null) {
            return "❌ 没找到对应的用户，请联系管理员";
        }
        SalesDailySummaryDO salesDailySummary = BotUtils.parseDailyReportToObject(update.getMessage().getText());
        salesDailySummary.setCreateUser(user.getId());

        try {
            salesDailySummaryService.telegramSave(salesDailySummary);
            return "✅ 商务日报提交成功！";
        } catch (Exception e) {
            log.error("保存日报失败", e);
            return "❌ 日报保存失败，请稍后重试";
        }
    }
}
