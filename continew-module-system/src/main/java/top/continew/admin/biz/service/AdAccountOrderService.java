/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import org.springframework.web.multipart.MultipartFile;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.query.*;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.util.List;

/**
 * 下户订单业务接口
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
public interface AdAccountOrderService extends BaseService<AdAccountOrderResp, AdAccountOrderDetailResp, AdAccountOrderQuery, AdAccountOrderReq>, IService<AdAccountOrderDO> {

    /**
     * 导入日报
     *
     * @param file
     * @param customerId
     */
    void importExcel(MultipartFile file, Long customerId);

    /**
     * 批量下户：按广告户类型
     *
     * @param req
     */
    void batchAddByBmType(AdAccountOrderBatchAddByBmTypeReq req);

    /**
     * 批量下户：按广告户ID
     *
     * @param req
     */
    void batchAddByAdAccountId(AdAccountOrderBatchAddByAdAccountIdReq req);

    /**
     * 接单
     *
     * @param id
     */
    void handle(Long id);

    /**
     * 取消
     *
     * @param id
     */
    void cancel(Long id);

    /**
     * 批量授权完成
     *
     * @param req
     */
    void batchAuthorize(IdsReq req);

    /**
     * 授权完成
     *
     * @param id
     */
    void authorizeSuccess(Long id);

    /**
     * 充值成功自动授权完成
     *
     * @param platformAdId
     */
    void authorizeSuccessByRecharge(String platformAdId);

    /**
     * 授权失败
     *
     * @param id
     */
    void authorizeFail(Long id);

    /**
     * 接收失败
     *
     * @param id
     */
    void receiveFail(Long id);

    /**
     * 作废
     *
     * @param id
     */
    void invalid(Long id);

    /**
     * 取消回收
     *
     * @param id
     */
    void cancelRecycle(Long id);

    /**
     * 批量修改bm id
     *
     * @param req
     */
    void batchChangeBmId(AdAccountOrderUpdateBmIdReq req);

    /**
     * 检查是否存在订单
     *
     * @param customerId
     * @param platformAdId
     * @return
     */
    void checkExistOrder(Long customerId, String platformAdId);

    /**
     * 获取广告户出售统计
     *
     * @param query
     * @param pageQuery
     * @return
     */
    BasePageResp<AdAccountSalesStatisticsResp> pageAdAccountSalesStatistics(AdAccountSalesStatisticsQuery query,
                                                                            PageQuery pageQuery);

    List<AdAccountSalesStatisticsResp> listAdAccountSalesStatistics(AdAccountSalesStatisticsQuery query);

    AdAccountSalesStatisticsResp getAdAccountSalesStatisticsSummary(AdAccountSalesStatisticsQuery query);

    /**
     * 回收订单
     *
     * @param id
     */
    void recycle(Long id);

    /**
     * 计算下户订单的总消耗
     *
     * @param statusEnum
     * @return
     */
    Integer calAccountOrderTotalSpent(AdAccountOrderStatusEnum statusEnum);

    List<DashboardChartCommonResp> selectTimezoneSaleStat(TimezoneSaleStatQuery query);

    /**
     * 查询客户订单列表
     *
     * @param customerId
     * @return
     */
    List<AdAccountOrderResp> selectCustomerUnClearOrderList(Long customerId);

    /**
     * 退款
     *
     * @param id
     */
    void refund(Long id);

    /**
     * 未消耗广告户
     *
     * @param query
     * @return
     */
    PageResp<UnSpentOrderResp> selectUnSpentOrderPage(InactiveAccountAnalyzeQuery query);

    /**
     * 未消耗广告户
     *
     * @param query
     * @return
     */
    List<UnSpentOrderResp> selectUnSpentOrderList(InactiveAccountAnalyzeQuery query);

    /**
     * 余额不足广告户
     *
     * @param query
     * @return
     */
    PageResp<InsufficientBalanceResp> selectInsufficientBalanceOrderPage(InsufficientBalanceAdAccountQuery query,
                                                                    PageQuery pageQuery);

    /**
     * 查询下户订单统计信息
     *
     * @param query
     * @return
     */
    List<AdAccountStatisticsResp> selectStatistics(AdAccountStatisticsQuery query);

    /**
     * 查询下户订单统计
     *
     * @param query
     * @return
     */
    List<AdAccountOrderStatisticsResp> selectOrderStatistics(AdAccountOrderStatisticsQuery query);

    /**
     * 检查预充账户余额情况
     */
    void checkPrepayBalance();

    /**
     * 检查广告设置
     */
    void checkAdConfig();

    /**
     * 客户接收广告户
     *
     * @param req
     */
    void receive(AdAccountOrderReceiveReq req);

    /**
     * 重置清零状态
     *
     * @param id
     */
    void resetClearStatus(Long id);
}