package top.continew.admin.biz.service;

import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.CustomerWithdrawOrderQuery;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderReq;
import top.continew.admin.biz.model.resp.CustomerWithdrawOrderDetailResp;
import top.continew.admin.biz.model.resp.CustomerWithdrawOrderResp;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderAuditReq;
import top.continew.admin.biz.model.req.CustomerWithdrawOrderRefundReq;

/**
 * 客户余额提现订单业务接口
 *
 * <AUTHOR>
 * @since 2025/01/22 14:23
 */
public interface CustomerWithdrawOrderService extends BaseService<CustomerWithdrawOrderResp, CustomerWithdrawOrderDetailResp, CustomerWithdrawOrderQuery, CustomerWithdrawOrderReq> {
    /**
     * 审核
     *
     * @param req 审核请求
     */
    void audit(CustomerWithdrawOrderAuditReq req);

    /**
     * 退款
     *
     * @param req 退款请求
     */
    void refund(CustomerWithdrawOrderRefundReq req);
}