package top.continew.admin.biz.service.crm;

import top.continew.admin.biz.model.resp.CustomerConditionResp;
import top.continew.admin.biz.model.query.crm.CustomerVisitTaskQuery;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskUpdateReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskCloseReq;
import top.continew.admin.biz.model.req.crm.CustomerVisitTaskDetailReq;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskDetailResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskDetailsResp;
import top.continew.admin.biz.model.resp.crm.CustomerVisitTaskResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户回访任务业务接口
 *
 * <AUTHOR>
 * @since 2025/06/05 14:54
 */
public interface CustomerVisitTaskService extends BaseService<CustomerVisitTaskResp, CustomerVisitTaskDetailResp, CustomerVisitTaskQuery, CustomerVisitTaskReq> {
    /**
     * 关闭客户回访任务
     *
     * @param req 关闭任务参数
     * @param id  任务ID
     */
    void closeTask(CustomerVisitTaskCloseReq req, Long id);
    
    /**
     * 检查客户是否存在有效的回访任务
     * 有效任务包括：待处理、处理中状态的任务，或7天内已完成的任务
     *
     * @param customerId 客户ID
     * @return 是否存在有效任务
     */
    boolean hasValidVisitTask(Long customerId);
    
    /**
     * 批量创建回访任务（带触发信息）
     *
     * @param customerResults 客户结果列表（包含触发条件信息）
     * @param strategyId 策略ID
     * @param strategyName 策略名称
     * @param requiredFinishTime 要求完成时间
     * @return 成功创建的任务数量
     */
    int batchCreateTasksWithTriggerInfo(List<CustomerConditionResp> customerResults,
                                        Long strategyId,
                                        String strategyName,
                                        LocalDateTime requiredFinishTime);
    
    // ========== 回访任务明细相关方法 ==========
    
    /**
     * 添加回访明细
     *
     * @param req 回访明细请求
     * @return 明细ID
     */
    Long addVisitDetail(CustomerVisitTaskDetailReq req);
    
    /**
     * 更新回访明细
     *
     * @param req 回访明细请求
     * @param id  明细ID
     */
    void updateVisitDetail(CustomerVisitTaskDetailReq req, Long id);
    
    /**
     * 删除回访明细
     *
     * @param id 明细ID
     */
    void deleteVisitDetail(Long id);
    
    /**
     * 获取任务的回访明细列表
     *
     * @param taskId 任务ID
     * @return 回访明细列表
     */
    List<CustomerVisitTaskDetailsResp> getVisitDetailsByTaskId(Long taskId);
}