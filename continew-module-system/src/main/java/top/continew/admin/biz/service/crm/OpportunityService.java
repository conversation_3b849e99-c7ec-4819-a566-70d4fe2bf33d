package top.continew.admin.biz.service.crm;

import top.continew.admin.biz.model.entity.crm.OpportunityDO;
import top.continew.admin.biz.model.query.crm.OpportunityQuery;
import top.continew.admin.biz.model.query.crm.OpportunityFollowQuery;
import top.continew.admin.biz.model.req.crm.*;
import top.continew.admin.biz.model.resp.crm.OpportunityDetailResp;
import top.continew.admin.biz.model.resp.crm.OpportunityResp;
import top.continew.admin.biz.model.resp.crm.OpportunityFollowResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.util.List;

/**
 * 商机业务接口
 *
 * <AUTHOR>
 * @since 2025/05/16 17:48
 */
public interface OpportunityService extends BaseService<OpportunityResp, OpportunityDetailResp, OpportunityQuery, OpportunityReq> {
    
    /**
     * 添加商机跟进记录
     *
     * @param req 商机跟进记录请求
     */
    void addFollow(OpportunityFollowReq req);
    
    /**
     * 修改商机跟进记录
     *
     * @param req 商机跟进记录更新请求
     */
    void updateFollow(OpportunityFollowUpdateReq req);
    
    /**
     * 删除商机跟进记录
     *
     * @param id 跟进记录ID
     */
    void deleteFollow(Long id);

    /**
     * 分页获取商机跟进记录
     *
     * @param query 商机跟进记录查询
     * @return 商机跟进记录分页数据
     */
    PageResp<OpportunityFollowResp> pageFollow(OpportunityFollowQuery query);
    
    /**
     * 批量修改商机对接人
     *
     * @param req 批量修改商机对接人请求
     */
    void batchUpdateHandler(OpportunityUpdateHandlerReq req);

    /**
     * 更新长期跟进状态
     */
    void updateLongTermFollowUp();

    /**
     * 获取客户的商机数据
     * @param customerId
     * @return
     */
    List<OpportunityDO> listCustomerOpportunity(Long customerId);
}