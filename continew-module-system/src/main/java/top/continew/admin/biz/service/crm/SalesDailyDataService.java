package top.continew.admin.biz.service.crm;

import com.baomidou.mybatisplus.extension.service.IService;
import top.continew.admin.biz.model.entity.crm.SalesDailyDataDO;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.crm.SalesDailyDataQuery;
import top.continew.admin.biz.model.req.crm.SalesDailyDataReq;
import top.continew.admin.biz.model.req.crm.SalesDailyDataConvertReq;
import top.continew.admin.biz.model.resp.crm.SalesDailyDataDetailResp;
import top.continew.admin.biz.model.resp.crm.SalesDailyDataResp;

import java.util.List;

/**
 * 商务每日数据业务接口
 *
 * <AUTHOR>
 * @since 2025/07/11 10:55
 */
public interface SalesDailyDataService extends BaseService<SalesDailyDataResp, SalesDailyDataDetailResp, SalesDailyDataQuery, SalesDailyDataReq>, IService<SalesDailyDataDO> {

    void batchAdd(List<SalesDailyDataReq> req);

    Long createLead(Long id);

    /**
     * 转化销售日报数据
     * @param req 转化请求参数
     */
    void convert(SalesDailyDataConvertReq req);
}