package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import net.dreamlu.mica.core.utils.BeanUtil;
import org.apache.commons.math3.dfp.DfpField;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.FbAdBidStragegyEnum;
import top.continew.admin.biz.enums.FbAdEffectiveStatusEnum;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.mapper.FbAdCampaignsMapper;
import top.continew.admin.biz.mapper.FbAdMapper;
import top.continew.admin.biz.mapper.FbAdSetsMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.req.AdAccountDoctorCheckReq;
import top.continew.admin.biz.model.resp.AdAccountDoctorCheckResp;
import top.continew.admin.biz.model.resp.CardTransactionResp;
import top.continew.admin.biz.service.*;
import top.continew.starter.core.exception.BusinessException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @version: 1.00.00
 * @description:
 * @date: 2025/7/8 16:52
 */
@Service
@RequiredArgsConstructor
public class AdAccountDoctorServiceImpl implements AdAccountDoctorService {
    private final AdAccountService adAccountService;
    private final AdAccountOrderService adAccountOrderService;
    private final CustomerService customerService;
    private final CardOpsStrategyFactory cardOpsStrategyFactory;
    private final AdAccountCardService adAccountCardService;
    private final CardService cardService;
    private final CustomerBalanceRecordService customerBalanceRecordService;
    private final CardTransactionService cardTransactionService;
    private final FbAdCampaignsMapper fbAdCampaignsMapper;
    private final FbAdSetsMapper fbAdSetsMapper;
    private final FbAdMapper fbAdMapper;

    @Override
    public List<AdAccountDoctorCheckResp> check(AdAccountDoctorCheckReq req) {
        List<AdAccountDoctorCheckResp> list = new ArrayList<>();
        List<AdAccountOrderDO> adAccountOrders = adAccountOrderService.list(new LambdaQueryWrapper<AdAccountOrderDO>().eq(AdAccountOrderDO::getStatus,
                AdAccountOrderStatusEnum.AUTH_COMPLETED).in(AdAccountOrderDO::getAdAccountId, req.getAdAccountIds()));

        List<AdAccountDO> adAccounts = adAccountService.list(new LambdaQueryWrapper<AdAccountDO>().in(AdAccountDO::getPlatformAdId, req.getAdAccountIds()));

        // 批量获取浏览器编号
        Map<String, String> browserNoMap = adAccountService.getBrowserNoByPlatformAdIds(req.getAdAccountIds());

        for (AdAccountOrderDO adAccountOrder : adAccountOrders) {
            AdAccountDoctorCheckResp resp = check(adAccountOrder,
                    adAccounts.stream()
                            .filter(row -> row.getPlatformAdId().equals(adAccountOrder.getAdAccountId()))
                            .findFirst()
                            .orElseThrow(() -> new BusinessException("未找到广告账户: " + adAccountOrder.getAdAccountId()))
            );

            // 设置浏览器编号
            resp.setBrowserNo(browserNoMap.get(adAccountOrder.getAdAccountId()));

            list.add(resp);
        }


        return list;
    }


    private AdAccountDoctorCheckResp check(AdAccountOrderDO adAccountOrder, AdAccountDO adAccount) {
        AdAccountDoctorCheckResp response = new AdAccountDoctorCheckResp();
        response.setAdAccountId(adAccountOrder.getAdAccountId());
        response.setTimezone(adAccount.getTimezone());
        response.setCustomerName(customerService.getName(adAccountOrder.getCustomerId()));

        // 检查是否一刀流（广告预算为1U）
        response.setIsOneDollar(adAccountOrder.getIsOneDollar());

        //获取广告户剩余余额
        BigDecimal fbBalance = adAccount.getSpendCap().subtract(adAccount.getAmountSpent());
        if (adAccountOrder.getEnablePrepay()) {
            BigDecimal rechargeAmount = customerBalanceRecordService.getTotalRechargeAmount(adAccountOrder.getCustomerId(), adAccountOrder.getAdAccountId(), adAccountOrder.getPayTime());
            fbBalance = rechargeAmount.subtract(adAccount.getAmountSpent());
        }

        response.setFbBalance(fbBalance);

        //获取广告户的卡余额
        AdAccountCardDO adAccountCard = adAccountCardService.getDefaultCard(adAccountOrder.getAdAccountId());
        CardDO card = cardService.getByCardNumber(adAccountCard.getFullCardNumber(), true);
        BigDecimal cardBalance = cardOpsStrategyFactory.findStrategy(adAccountCard.getPlatform()).getCardBalance(card);


        response.setCardBalance(cardBalance);

        //最后一条交易流水
        CardTransactionDO lastTrans = cardTransactionService.getOne(new LambdaQueryWrapper<CardTransactionDO>()
                .eq(CardTransactionDO::getAdAccountId, adAccountOrder.getAdAccountId())
                .eq(CardTransactionDO::getCustomerId, adAccountOrder.getCustomerId())
                .orderByDesc(CardTransactionDO::getStatTime).last("limit 1"));
        response.setLastTrans(BeanUtil.copyProperties(lastTrans, CardTransactionResp.class));

        // 统计广告相关数据并直接设置到响应对象
        calculateAdStatistics(adAccountOrder.getAdAccountId(), response);

        return response;
    }

    /**
     * 计算广告统计数据并直接设置到响应对象
     * @param adAccountId 广告账户ID
     * @param response 响应对象
     */
    private void calculateAdStatistics(String adAccountId, AdAccountDoctorCheckResp response) {
        List<FbAdDO> ads = fbAdMapper.selectByAdAccountId(adAccountId);
        List<FbAdSetsDO> adSets = fbAdSetsMapper.selectByAdAccountId(adAccountId);
        List<FbAdCampaignsDO> campaigns = fbAdCampaignsMapper.selectByAdAccountId(adAccountId);

        // 设置基础统计数据
        response.setAdCount(ads != null ? ads.size() : 0);
        response.setCampaignCount(campaigns != null ? campaigns.size() : 0);
        response.setAdSetCount(adSets != null ? adSets.size() : 0);

        if (response.getAdCount() == 0) {
            return;
        }

        // 统一遍历广告列表，同时计算状态统计和预算统计
        calculateAdStatisticsInOnePass(ads, adSets, campaigns, response);
    }

    /**
     * 一次遍历计算广告状态统计和预算统计，提高性能
     * @param ads 广告列表
     * @param adSets 广告组列表
     * @param campaigns 广告系列列表
     * @param response 响应对象
     */
    private void calculateAdStatisticsInOnePass(List<FbAdDO> ads, 
                                               List<FbAdSetsDO> adSets, 
                                               List<FbAdCampaignsDO> campaigns,
                                               AdAccountDoctorCheckResp response) {
        // 处理空列表情况
        if (CollUtil.isEmpty(ads)) {
            setDefaultStatistics(response);
            return;
        }
        
        // 状态统计变量
        int activeCount = 0;
        int rejectCount = 0;
        int prepareCount = 0;
        int previewCount = 0;
        int learningCount = 0;
        
        // 预算统计变量
        long totalBudgetCents = 0;
        int costCapCount = 0;
        
        // 一次遍历同时计算状态和预算统计
        for (FbAdDO ad : ads) {
            // 统计广告状态
            if (StrUtil.isNotBlank(ad.getEffectiveStatus())) {
                try {
                    FbAdEffectiveStatusEnum effectiveStatus = FbAdEffectiveStatusEnum.valueOf(ad.getEffectiveStatus());
                    switch (effectiveStatus) {
                        case DISAPPROVED:
                            rejectCount++;
                            break;
                        case ACTIVE:
                            activeCount++;
                            break;
                        case IN_PROCESS:
                            prepareCount++;
                            break;
                        case PENDING_REVIEW:
                            previewCount++;
                            break;
                        default:
                            break;
                    }
                } catch (IllegalArgumentException e) {
                    // 忽略无效的状态值
                }
            }
            
            // 计算预算和成效目标统计
            if (StrUtil.isNotBlank(ad.getAdsetId())) {
                // 查找对应的广告组
                Optional<FbAdSetsDO> adSetOpt = null == adSets ? Optional.empty() : adSets.stream()
                        .filter(set -> StrUtil.isNotBlank(ad.getAdsetId()) && ad.getAdsetId().equals(set.getPlatformId()))
                        .findFirst();

                if (adSetOpt.isPresent()) {
                    FbAdSetsDO adSet = adSetOpt.get();
                    
                    // 计算预算
                    Long budget = getBudgetFromAdSet(adSet);
                    if (budget == null) {
                        // 从广告系列获取预算
                        budget = getBudgetFromCampaign(adSet.getCampaignId(), campaigns);
                    }
                    
                    if (budget != null) {
                        totalBudgetCents += budget;
                    }

                    // 统计成效目标
                    if (isCostCapStrategy(adSet, campaigns)) {
                        costCapCount++;
                    }
                }
            }
        }
        
        // 设置统计结果
        response.setActiveAdCount(activeCount);
        response.setRejectAdCount(rejectCount);
        response.setPrepareAdCount(prepareCount);
        response.setPreviewAdCount(previewCount);
        response.setLearningAdCount(learningCount);
        response.setCostCapCount(costCapCount);

        // 计算并设置平均预算（分转换为元）
        if (totalBudgetCents > 0 && !ads.isEmpty()) {
            BigDecimal totalBudgetDollars = BigDecimal.valueOf(totalBudgetCents)
                    .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            response.setAverageBudget(totalBudgetDollars
                    .divide(BigDecimal.valueOf(ads.size()), 2, RoundingMode.HALF_UP));
        } else {
            response.setAverageBudget(BigDecimal.ZERO);
        }
    }

    /**
     * 从广告组获取预算
     * @param adSet 广告组
     * @return 预算（分）
     */
    private Long getBudgetFromAdSet(FbAdSetsDO adSet) {
        if (adSet.getDailyBudget() != null && adSet.getDailyBudget() > 0) {
            return adSet.getDailyBudget().longValue();
        }
        if (adSet.getLifetimeBudget() != null && adSet.getLifetimeBudget() > 0) {
            return adSet.getLifetimeBudget().longValue();
        }
        return null;
    }

    /**
     * 从广告系列获取预算
     * @param campaignId 广告系列ID
     * @param campaigns 广告系列列表
     * @return 预算（分）
     */
    private Long getBudgetFromCampaign(String campaignId, List<FbAdCampaignsDO> campaigns) {
        Optional<FbAdCampaignsDO> campaignOpt = null == campaigns ? Optional.empty() : campaigns.stream()
                .filter(c -> StrUtil.isNotBlank(campaignId) && c.getPlatformId().equals(campaignId))
                .findFirst();
        
        if (campaignOpt.isPresent()) {
            FbAdCampaignsDO campaign = campaignOpt.get();
            if (campaign.getDailyBudget() != null && campaign.getDailyBudget() > 0) {
                return campaign.getDailyBudget().longValue();
            }
            if (campaign.getLifetimeBudget() != null && campaign.getLifetimeBudget() > 0) {
                return campaign.getLifetimeBudget().longValue();
            }
        }
        return null;
    }

    /**
     * 判断是否为成效目标策略
     * @param adSet 广告组
     * @param campaigns 广告系列列表
     * @return 是否为成效目标策略
     */
    private boolean isCostCapStrategy(FbAdSetsDO adSet, List<FbAdCampaignsDO> campaigns) {
        // 先检查广告组的竞价策略
        if (StrUtil.isNotBlank(adSet.getBidStrategy()) && 
            FbAdBidStragegyEnum.COST_CAP.getValue().equals(adSet.getBidStrategy())) {
            return true;
        }
        
        // 如果广告组没有设置，检查广告系列的竞价策略
        Optional<FbAdCampaignsDO> campaignOpt = campaigns.stream()
                .filter(c -> c.getPlatformId().equals(adSet.getCampaignId()))
                .findFirst();
        
        return campaignOpt.isPresent() && 
               StrUtil.isNotBlank(campaignOpt.get().getBidStrategy()) &&
               FbAdBidStragegyEnum.COST_CAP.getValue().equals(campaignOpt.get().getBidStrategy());
    }


    /**
     * 设置默认统计数据
     * @param response 响应对象
     */
    private void setDefaultStatistics(AdAccountDoctorCheckResp response) {
        response.setActiveAdCount(0);
        response.setRejectAdCount(0);
        response.setPrepareAdCount(0);
        response.setPreviewAdCount(0);
        response.setLearningAdCount(0);
        response.setAverageBudget(BigDecimal.ZERO);
        response.setCostCapCount(0);
    }

}
