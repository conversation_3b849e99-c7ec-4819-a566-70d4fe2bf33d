/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.telegram.telegrambots.meta.api.methods.ParseMode;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.AdAccountStatusChangeEvent;
import top.continew.admin.biz.event.AdAccountStolenEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.excel.ProducerRecordsExcelData;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.AdAccountMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.query.AdAccountQuery;
import top.continew.admin.biz.model.query.InactiveAccountAnalyzeQuery;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.robot.utils.BotUtils;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.*;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.entity.BaseDO;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.BasePageResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 广告账号业务实现
 *
 * <AUTHOR>
 * @since 2024/12/30 17:50
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdAccountServiceImpl extends BaseServiceImpl<AdAccountMapper, AdAccountDO, AdAccountResp, AdAccountDetailResp, AdAccountQuery, AdAccountReq> implements AdAccountService {

    private final AdAccountBalanceRecordService adAccountBalanceRecordService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    private final UserService userService;

    private final AdAccountCardService adAccountCardService;

    private final CardService cardService;

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    private final Pattern pattern = Pattern.compile("\\d+");

    private final static Map<String, String> timeZoneMap;

    private final ThreadPoolExecutor threadPoolExecutor = ThreadPoolHelper.getOrderInstance();
    private final ThreadPoolExecutor adSpentCheckPoolExecutor = ThreadPoolHelper.getAdAccountSpentCheckInstance();

    private final AdAccountBrowserLogService adAccountBrowserLogService;

    private final TagRelationService tagRelationService;

    private final FbAccountService fbAccountService;

    private final AdAccountHelper adAccountHelper;

    static {
        timeZoneMap = new HashMap<>();
        timeZoneMap.put("-3：巴西", "GMT-03:00");
        timeZoneMap.put("5.3：印度", "GMT+05:30");
        timeZoneMap.put("8：中国", "GMT+08:00");
        timeZoneMap.put("-7：美国", "GMT-07:00");
        timeZoneMap.put("-8：美国", "GMT-08:00");
        timeZoneMap.put("-5", "GMT-05:00");
        timeZoneMap.put("-4：美洲", "GMT-04:00");
        timeZoneMap.put("7：亚洲", "GMT+07:00");
        timeZoneMap.put("3", "GMT+03:00");
        timeZoneMap.put("5", "GMT+05:00");
        timeZoneMap.put("6", "GMT+06:00");
        timeZoneMap.put("utc:00", "GMT+00:00");
        timeZoneMap.put("-8", "GMT-08:00");
        timeZoneMap.put("-2", "GMT-02:00");
        timeZoneMap.put("刚果+1", "GMT+01:00");
    }

    @Override
    public void export(AdAccountQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<AdAccountResp> list = this.baseMapper.selectCustomList(getWrapper(query));
        List<AdAccountDetailResp> excelList = new ArrayList<>();
        for (AdAccountResp adAccountDO : list) {
            AdAccountDetailResp adAccountDetailResp = new AdAccountDetailResp();
            BeanUtil.copyProperties(adAccountDO, adAccountDetailResp);
            excelList.add(adAccountDetailResp);
        }
        ExcelUtils.export(excelList, "导出数据", AdAccountDetailResp.class, response);
    }

    private QueryWrapper<AdAccountDO> getWrapper(AdAccountQuery query) {
        QueryWrapper<AdAccountDO> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(query.getBm1Browser()), "baa.bm1_browser", query.getBm1Browser())
            .in(CollUtil.isNotEmpty(query.getBmIds()), "baa.bm_id", query.getBmIds())
            .eq(query.getBm1Id() != null, "baa.bm1_id", query.getBm1Id())
            .in(CollectionUtils.isNotEmpty(query.getAccountStatus()), "baa.account_status", query.getAccountStatus())
            .in(CollectionUtils.isNotEmpty(query.getKeepStatus()), "baa.keep_status", query.getKeepStatus())
            .in(CollectionUtils.isNotEmpty(query.getUnusableReason()), "baa.unusable_reason", query.getUnusableReason())
            .eq(query.getSaleStatus() != null, "baa.sale_status", query.getSaleStatus())
            .eq(query.getUsable() != null, "baa.usable", query.getUsable())
            .eq(query.getIsRemoveAdmin() != null, "baa.is_remove_admin", query.getIsRemoveAdmin())
            .eq(query.getIsLowLimit() != null, "baa.is_low_limit", query.getIsLowLimit())
            .eq(query.getAppealStatus() != null, "baa.appeal_status", query.getAppealStatus())
            .eq(query.getClearStatus() != null, "baa.clear_status", query.getClearStatus())
            .in(CollectionUtils.isNotEmpty(query.getBmItemType()), "baa.bm_item_type", query.getBmItemType())
            .eq(query.getVoStatus() != null, "baa.vo_status", query.getVoStatus())
            .like(StringUtils.isNotBlank(query.getTag()) && !Objects.equals(query.getTag(), "空"), "baa.tag", query.getTag())
            .isNull(Objects.equals(query.getTag(), "空"), "baa.tag")
            .in(CollUtil.isNotEmpty(query.getPlatformAdIds()), "baa.platform_ad_id", query.getPlatformAdIds())
            .eq(StringUtils.isNotBlank(query.getTimezone()), "baa.timezone", query.getTimezone())
            .between(ObjectUtil.isAllNotEmpty(query.getFirstSpend(), query.getLastSpend()), "baa.amount_spent", query.getFirstSpend(), query.getLastSpend())
            .between(ObjectUtil.isAllNotEmpty(query.getStartTime(), query.getEndTime()), "baa.create_time", query.getStartTime(), query.getEndTime())
            .in(CollUtil.isNotEmpty(query.getCreateUser()), "baa.create_user", query.getCreateUser())
            .eq(query.getFbChannelId() != null, "fa.channel_id", query.getFbChannelId())
            .eq(StringUtils.isNotBlank(query.getChannelName()), "bbmc.name", query.getChannelName());
        if (StringUtils.isNotBlank(query.getRemark())) {
            if ("空".equals(query.getRemark())) {
                wrapper.eq("baa.remark", "");
            } else {
                wrapper.like("baa.remark", query.getRemark());
            }
        }
        if (ArrayUtils.isNotEmpty(query.getBmAuthTime())) {
            wrapper.eq("baa.keep_status", AdAccountKeepStatusEnum.SUCCESS)
                .between("baa.bm_auth_time", query.getBmAuthTime()[0], query.getBmAuthTime()[1]);
        }
        if (ArrayUtils.isNotEmpty(query.getBanTime())) {
            wrapper.eq("baa.account_status", AdAccountStatusEnum.BANNED)
                .between("baa.ban_time", query.getBanTime()[0], query.getBanTime()[1]);
        }
        if (ArrayUtils.isNotEmpty(query.getSaleTime())) {
            wrapper.between("baa.sale_time", query.getSaleTime()[0], query.getSaleTime()[1]);
        }
        if (ArrayUtils.isNotEmpty(query.getUpdateTime())) {
            wrapper.between("baa.update_time", query.getUpdateTime()[0], query.getUpdateTime()[1]);
        }
        if (StringUtils.isNotBlank(query.getFullCardNumber())) {
            List<String> ids = adAccountCardService.list(new LambdaQueryWrapper<AdAccountCardDO>().eq(AdAccountCardDO::getFullCardNumber, query.getFullCardNumber()))
                .stream()
                .map(AdAccountCardDO::getPlatformAdId)
                .toList();
            wrapper.in(CollectionUtils.isNotEmpty(ids), "baa.platform_ad_id", ids);
            wrapper.eq(CollectionUtils.isEmpty(ids), "baa.platform_ad_id", "-1");
        }
        if (StringUtils.isNotBlank(query.getBrowserNo())) {
            List<String> nos = StrUtil.split(query.getBrowserNo().replace(",", " "), StringUtils.SPACE, true, true);
            wrapper.in(!nos.isEmpty(), "baa.browser_no", nos);
        }
        if (query.getDeptId() != null) {
            List<Long> userIds = userService.listObjs(Wrappers.<UserDO>lambdaQuery()
                .select(UserDO::getId)
                .eq(UserDO::getDeptId, query.getDeptId()));
            if (userIds.isEmpty()) {
                wrapper.eq("baa.create_user", -1);
            } else {
                wrapper.in("baa.create_user", userIds);
            }
        }
        if (CollUtil.isNotEmpty(query.getTags())) {
            List<Long> adAccountIds = tagRelationService.listAdAccountId(query.getTags());
            if (adAccountIds.isEmpty()) {
                wrapper.eq("baa.id", -1);
            } else {
                wrapper.in("baa.id", adAccountIds);
            }
        }
        return wrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveExcelData(List<ProducerRecordsExcelData> cachedDataList) {

    }

    @Override
    public AdAccountDO getAdAccountByFbAccountId(String fbAccountId) {
        return getOne(new LambdaQueryWrapper<AdAccountDO>().eq(AdAccountDO::getPlatformAccountId, fbAccountId));
    }

    @Override
    public AdAccountDO getByPlatformAdId(String platformAdId) {
        return this.getOne(new LambdaQueryWrapper<AdAccountDO>().eq(AdAccountDO::getPlatformAdId, platformAdId));
    }

    @Override
    public void addCard(AdAccountAddCardReq req) {
        AdAccountCardDO adAccountCardDO = new AdAccountCardDO();
        adAccountCardDO.setPlatformAdId(req.getAdAccountId());
        adAccountCardDO.setFullCardNumber(req.getCardNo());
        adAccountCardDO.setPlatform(req.getPlatform());
        adAccountCardService.save(adAccountCardDO);
    }

    @Override
    public void update(AdAccountReq req, Long id) {
        this.beforeUpdate(req, id);
        AdAccountDO entity = this.getById(id);
        CheckUtils.throwIf(req.getSaleStatus().equals(AdAccountSaleStatusEnum.RECYCLE) && !entity.getSaleStatus()
            .equals(AdAccountSaleStatusEnum.RECYCLE), "回收状态无法手动修改");
        BeanUtil.copyProperties(req, entity, CopyOptions.create().ignoreNullValue());
        this.updateById(entity);
        this.afterUpdate(req, entity);
    }

    @Override
    public BasePageResp<AdAccountResp> customPage(AdAccountQuery query, PageQuery pageQuery) {
        QueryWrapper<AdAccountDO> queryWrapper = getWrapper(query);
        this.sort(queryWrapper, pageQuery);
        IPage<AdAccountResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        PageResp<AdAccountResp> pageResp = PageResp.build(page, this.getListClass());
        pageResp.getList().forEach(this::fill);
        return pageResp;
    }

    @Override
    public void changeAmount(String platformAdId,
                             BigDecimal amount,
                             AdAccountBalanceTypeEnum balanceType,
                             LocalDateTime transactionTime,
                             String remark) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        AdAccountBalanceRecordDO record = new AdAccountBalanceRecordDO();
        record.setPlatformAdId(platformAdId);
        boolean isOut = amount.compareTo(BigDecimal.ZERO) < 0;
        if (isOut) {
            record.setAction(TransactionActionEnum.OUT);
        } else {
            record.setAction(TransactionActionEnum.IN);
        }
        record.setType(balanceType);
        record.setAmount(amount.abs());
        record.setTransTime(transactionTime != null ? transactionTime : LocalDateTime.now());
        record.setRemark(remark);
        adAccountBalanceRecordService.save(record);
    }

    @Override
    public List<AdAccountInventoryResp> getAdAccountInventory() {
        return this.baseMapper.getAdAccountInventory(false);
    }

    @Override
    public void updateBrowserNo(String platformAdId, String browser) {
        this.baseMapper.updateBrowserNo(platformAdId, browser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importAdAccount(MultipartFile file) {
        List<ProducerRecordsExcelData> importRowList;
        // 读取表格数据
        try {
            importRowList = EasyExcel.read(file.getInputStream())
                .head(ProducerRecordsExcelData.class)
                .sheet("FB客户关联记录")
                .headRowNumber(3)
                .doReadSync();
        } catch (Exception e) {
            log.error("FB客户关联记录数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }
        List<CardDO> cards = cardService.list();
        Map<String, CardPlatformEnum> cardPlatformEnumMap = cards.stream()
            .collect(Collectors.toMap(CardDO::getCardNumber, CardDO::getPlatform));

        List<AdAccountCardDO> saveCardList = new ArrayList<>();
        List<AdAccountDO> adAccountList = new ArrayList<>();
        List<AdAccountDO> existList = this.list();
        for (ProducerRecordsExcelData producerRecordsExcelData : importRowList) {
            if (StringUtils.isBlank(producerRecordsExcelData.getAdAccountId()) || !StringUtils.isNumeric(producerRecordsExcelData.getAdAccountId())) {
                continue;
            }
            AdAccountDO exist = existList.stream()
                .filter(v -> v.getPlatformAdId().equals(producerRecordsExcelData.getAdAccountId()))
                .findFirst()
                .orElse(null);
            AdAccountDO adAccount = new AdAccountDO();
            adAccount.setBmId(producerRecordsExcelData.getBm5());
            String timeZone = timeZoneMap.get(producerRecordsExcelData.getTimeZone());
            if (StringUtils.isNotBlank(timeZone)) {
                adAccount.setTimezone(timeZone);
            } else {
                adAccount.setTimezone(producerRecordsExcelData.getTimeZone());
            }
            adAccount.setPersonalArea(producerRecordsExcelData.getPersonalArea());
            if (StringUtils.isNotBlank(producerRecordsExcelData.getOpsBrowser())) {
                adAccount.setBrowserNo(producerRecordsExcelData.getOpsBrowser().replace("-", ","));
            }
            adAccount.setCreateUser(StpUtil.getLoginIdAsLong());
            AdAccountExcelStatus adAccountExcelStatus = AdAccountExcelStatus.getAdAccountExcelStatus(producerRecordsExcelData.getStatus());
            if (adAccountExcelStatus != null) {
                adAccount.setAccountStatus(adAccountExcelStatus.getAdAccountStatus());
                adAccount.setSaleStatus(adAccountExcelStatus.getSaleStatus());
                adAccount.setKeepStatus(adAccountExcelStatus.getKeepingAdAccountStatus());
            } else {
                adAccount.setAccountStatus(AdAccountStatusEnum.BANNED);
                adAccount.setSaleStatus(AdAccountSaleStatusEnum.WAIT);
            }
            adAccount.setKeepStatus(AdAccountKeepStatusEnum.SUCCESS);
            adAccount.setRemark(producerRecordsExcelData.getRemark());
            if (exist == null) {
                adAccount.setPlatformAdId(producerRecordsExcelData.getAdAccountId());
                adAccount.setAccountStatus(adAccount.getAccountStatus());
                adAccount.setSaleStatus(adAccount.getSaleStatus());
                adAccount.setTimezone(adAccount.getTimezone());
                if (StringUtils.isNotBlank(producerRecordsExcelData.getCvCardNumber())) {
                    saveCardList.addAll(createCard(producerRecordsExcelData.getAdAccountId(), producerRecordsExcelData.getCvCardNumber(), false));
                }
                if (StringUtils.isNotBlank(producerRecordsExcelData.getHuitongCardNumber())) {
                    saveCardList.addAll(createCard(producerRecordsExcelData.getAdAccountId(), producerRecordsExcelData.getHuitongCardNumber(), false));
                }
            } else {
                adAccount.setId(exist.getId());
            }
            adAccountList.add(adAccount);
        }
        this.saveOrUpdateBatch(adAccountList);
        for (AdAccountCardDO cardDO : saveCardList) {
            cardDO.setPlatform(cardPlatformEnumMap.getOrDefault(cardDO.getFullCardNumber(), null));
        }
        adAccountCardService.saveBatch(saveCardList);
    }

    @Override
    public Set<String> getCopyBrowserSet(AdAccountQuery query) {
        QueryWrapper<AdAccountDO> wrapper;
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            wrapper = new QueryWrapper<>();
            wrapper.in("baa.id", query.getIds());
        } else {
            wrapper = getWrapper(query);
        }
        Set<String> set = this.baseMapper.selectCustomList(wrapper)
            .stream()
            .map(AdAccountResp::getBrowserNo)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(set)) {
            return Collections.emptySet();
        }
        Set<String> normalSet = list(new LambdaQueryWrapper<AdAccountDO>().eq(AdAccountDO::getAccountStatus, AdAccountStatusEnum.NORMAL.getValue())).stream()
            .map(AdAccountDO::getBrowserNo)
            .collect(Collectors.toSet());
        set.removeAll(normalSet);
        return set;
    }

    @Override
    public AdAccountCardOpsResultResp rechargeMasterCard(String platformAdId,
                                                         BigDecimal amount,
                                                         boolean inactiveCheck) {
        AdAccountCardDO defaultCard;
        try {
            defaultCard = adAccountCardService.getDefaultCard(platformAdId);
        } catch (Exception e) {
            return AdAccountCardOpsResultResp.builder().isSuccess(false).message("默认卡片设置错误，请检查").build();
        }
        if (defaultCard == null) {
            return AdAccountCardOpsResultResp.builder().isSuccess(false).message("请先设置广告户关联主卡片").build();
        }
        if (StringUtils.isBlank(defaultCard.getFullCardNumber()) || defaultCard.getPlatform() == null) {
            return AdAccountCardOpsResultResp.builder().isSuccess(false).message("主卡信息待完善").build();
        }
        log.info("【广告户充值】[{}]开始充值...", platformAdId);
        log.info("【广告户充值】[{}]主卡：{}，充值金额：{}", platformAdId, defaultCard.getFullCardNumber(), amount);
        try {
            CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(defaultCard.getPlatform());
            CardDO cardDO = cardService.getByCardNumber(defaultCard.getFullCardNumber(), false);
            if (inactiveCheck && !cardDO.getStatus().equals(CardStatusEnum.NORMAL)) {
                return AdAccountCardOpsResultResp.builder().isSuccess(false).message("请联系管理员激活卡片").build();
            }
            cardOpsStrategy.rechargeCard(cardDO, amount);
            log.info("【广告户充值】[{}]充值成功", platformAdId);
            cardService.addCardBalance(cardDO.getId(), amount);
        } catch (Exception e) {
            log.info("【广告户充值】[{}]充值失败：{}", platformAdId, e.getMessage());
            return AdAccountCardOpsResultResp.builder().isSuccess(false).message(e.getMessage()).build();
        }
        return AdAccountCardOpsResultResp.builder()
            .isSuccess(true)
            .cardNumber(defaultCard.getFullCardNumber())
            .platform(defaultCard.getPlatform())
            .amount(amount)
            .build();
    }

    @Override
    public AdAccountCardOpsResultResp withdrawMasterCard(String platformAdId, BigDecimal amount) {
        AdAccountCardDO defaultCard;
        try {
            defaultCard = adAccountCardService.getDefaultCard(platformAdId);
        } catch (Exception e) {
            return AdAccountCardOpsResultResp.builder().isSuccess(false).message("默认卡片设置错误，请检查").build();
        }
        if (defaultCard == null) {
            return AdAccountCardOpsResultResp.builder().isSuccess(false).message("请先设置广告户关联主卡片").build();
        }
        if (StringUtils.isBlank(defaultCard.getFullCardNumber()) || defaultCard.getPlatform() == null) {
            return AdAccountCardOpsResultResp.builder().isSuccess(false).message("主卡信息待完善").build();
        }
        log.info("【广告户取款】[{}]开始取款...", platformAdId);
        log.info("【广告户取款】[{}]主卡：{}，取款金额：{}", platformAdId, defaultCard.getFullCardNumber(), amount);
        try {
            CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(defaultCard.getPlatform());
            CardDO cardDO = cardService.getByCardNumber(defaultCard.getFullCardNumber(), false);
            BigDecimal withdrawAmount = cardOpsStrategy.withdrawCard(cardDO, amount);
            log.info("【广告户取款】[{}]取款成功", platformAdId);
            cardService.subCardBalance(cardDO.getId(), withdrawAmount);
        } catch (Exception e) {
            log.info("【广告户取款】[{}]取款失败：{}", platformAdId, e.getMessage());
            return AdAccountCardOpsResultResp.builder().isSuccess(false).message(e.getMessage()).build();
        }
        return AdAccountCardOpsResultResp.builder()
            .isSuccess(true)
            .cardNumber(defaultCard.getFullCardNumber())
            .platform(defaultCard.getPlatform())
            .amount(amount)
            .build();
    }

    @Override
    public List<AdAccountCardOpsResultResp> withdrawAllCards(String platformAdId) {
        List<AdAccountCardDO> cards = adAccountCardService.listValidByPlatformAdId(platformAdId);
        if (cards.isEmpty()) {
            return new ArrayList<>();
        }
        log.info("【广告户取款】[{}]开始取款...", platformAdId);
        log.info("【广告户取款】[{}]一共绑定{}张卡片", platformAdId, cards.size());
        List<AdAccountCardOpsResultResp> list = new ArrayList<>();
        for (AdAccountCardDO card : cards) {
            AdAccountCardOpsResultResp.AdAccountCardOpsResultRespBuilder builder = AdAccountCardOpsResultResp.builder();
            builder.cardNumber(StringUtils.defaultIfBlank(card.getFullCardNumber(), card.getFuzzyCardNumber()))
                .platform(card.getPlatform());
            if (StringUtils.isBlank(card.getFullCardNumber()) || card.getPlatform() == null) {
                builder.isSuccess(false).message("卡片信息待完善");
            } else {
                log.info("【广告户取款】[{}]卡片{}开始取款...", platformAdId, card.getFullCardNumber());
                try {
                    CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(card.getPlatform());
                    CardDO cardDO = cardService.getByCardNumber(card.getFullCardNumber(), false);
                    BigDecimal withdrawAmount = cardOpsStrategy.withdrawCard(cardDO, null);
                    log.info("【广告户取款】[{}]卡片{}取款成功，取款金额：{}", platformAdId, card.getFullCardNumber(), withdrawAmount);
                    cardService.subCardBalance(cardDO.getId(), withdrawAmount);
                    builder.isSuccess(true).amount(withdrawAmount);
                } catch (Exception e) {
                    log.info("【广告户取款】[{}]卡片{}取款失败：{}", platformAdId, card.getFullCardNumber(), e.getMessage());
                    builder.isSuccess(false).message(e.getMessage());
                }
            }
            list.add(builder.build());
        }
        return list;
    }

    @Override
    public void updateCardStatus(String platformAdId, boolean isActive) {
        List<AdAccountCardDO> cards = adAccountCardService.listValidByPlatformAdId(platformAdId);
        if (cards.isEmpty()) {
            return;
        }
        String ops = isActive ? "激活" : "冻结";
        log.info("【广告户改卡】[{}]开始{}...", platformAdId, ops);
        log.info("【广告户改卡】[{}]一共绑定{}张卡片", platformAdId, cards.size());
        for (AdAccountCardDO card : cards) {
            if (StringUtils.isNotBlank(card.getFullCardNumber()) && card.getPlatform() != null) {
                try {
                    CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(card.getPlatform());
                    CardDO cardDO = cardService.getByCardNumber(card.getFullCardNumber(), false);
                    if (isActive) {
                        cardOpsStrategy.activeCard(cardDO);
                        cardService.update(Wrappers.<CardDO>lambdaUpdate()
                            .set(CardDO::getStatus, CardStatusEnum.NORMAL)
                            .eq(CardDO::getCardNumber, card.getFullCardNumber()));
                    } else {
                        cardOpsStrategy.inactiveCard(cardDO);
                        cardService.update(Wrappers.<CardDO>lambdaUpdate()
                            .set(CardDO::getStatus, CardStatusEnum.FROZEN)
                            .eq(CardDO::getCardNumber, card.getFullCardNumber()));
                    }
                } catch (Exception e) {
                    log.info("【广告户改卡】[{}]卡片{}{}失败：{}", platformAdId, card.getFullCardNumber(), ops, e.getMessage());
                }
            }
        }
    }

    @Override
    public void syncAllSpend(String envId, String serialNumber, String proxy, String headers, boolean isLite) {
        String response;
        if (StringUtils.isNotBlank(serialNumber)) {
            response = RabbitUtils.getAllAdSpentV2(serialNumber, isLite, proxy, headers);
        } else {
            response = RabbitUtils.getAllAdSpent(envId, isLite);
        }
        String browserKey = StringUtils.defaultIfBlank(envId, serialNumber);
        JSONObject jsonObject = JSONObject.parseObject(response);
        String code = jsonObject.getString("code");
        if (!"0".equals(code)) {
            log.error("【{}】总消耗同步失败：{}", browserKey, jsonObject);
            String message = StringUtils.defaultIfBlank(jsonObject.getString("msg"), jsonObject.getString("message"));
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(telegramChatIdConfig.getAdAccountNotifyChatId())
                .text(browserKey + " 总消耗数据同步失败：" + message.replace("http", "htp"))
                .build()));
            return;
        }
        List<AdAccountDO> adAccountList = this.list(Wrappers.<AdAccountDO>lambdaQuery()
            .select(AdAccountDO::getId, AdAccountDO::getPlatformAdId, AdAccountDO::getAccountStatus, AdAccountDO::getIsPrepay, AdAccountDO::getSaleStatus, AdAccountDO::getUsable, AdAccountDO::getSpendCap, AdAccountDO::getParentBrowserNo, AdAccountDO::getRealAdtrustDsl, AdAccountDO::getPrepayAccountBalance, AdAccountDO::getRemark));
        List<AdAccountDO> oldAccountList = adAccountList.stream()
            .filter(v -> v.getParentBrowserNo().equals(browserKey))
            .toList();
        List<AdAccountDO> updateList = new ArrayList<>();
        JSONArray data = jsonObject.getJSONArray("data");
        List<String> currentAccountList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject dataItem = data.getJSONObject(i);
            int itemCode = dataItem.getIntValue("code");
            JSONObject item = dataItem.getJSONObject("body");
            String platformAdId = item.getString("account_id");
            currentAccountList.add(platformAdId);
            if (itemCode != 200) {
                log.error("【{}】{}总消耗同步失败：{}", browserKey, platformAdId, item);
                continue;
            }
            String name = item.getString("name");
            int accountStatus = item.getInteger("account_status");
            JSONObject insights = item.getJSONObject("insights");
            BigDecimal totalSpent = BigDecimal.ZERO;
            if (insights != null) {
                JSONArray insightData = insights.getJSONArray("data");
                if (insightData != null && !insightData.isEmpty()) {
                    totalSpent = insightData.getJSONObject(0).getBigDecimal("spend");
                }
            }
            AdAccountDO exist = adAccountList.stream()
                .filter(v -> v.getPlatformAdId().equals(platformAdId))
                .findFirst()
                .orElse(null);
            if (exist != null) {
                AdAccountDO update = new AdAccountDO();
                update.setId(exist.getId());
                update.setName(name);
                BigDecimal spendCap = CommonUtils.divide100(item.getBigDecimal("spend_cap"), null);
                update.setAmountSpent(CommonUtils.divide100(item.getBigDecimal("amount_spent"), null));
                update.setSpendCap(spendCap);
                update.setBalance(CommonUtils.divide100(item.getBigDecimal("balance"), null));
                update.setTotalSpent(totalSpent);
                BigDecimal dsl = item.getBigDecimal("adtrust_dsl");
                update.setRealAdtrustDsl(dsl);
                update.setParentBrowserNo(browserKey);
                update.setDisabledReason(item.getString("disable_reason"));
                JSONObject prepayAccountBalanceJson = item.getJSONObject("prepay_account_balance");
                if (prepayAccountBalanceJson != null) {
                    BigDecimal prepayAccountBalance = prepayAccountBalanceJson.getBigDecimal("amount");
                    update.setPrepayAccountBalance(prepayAccountBalance);
                    adAccountHelper.setAdAccountLastPrepayBalance(exist.getPlatformAdId(), exist.getPrepayAccountBalance());
                }
                JSONObject adspaymentcycle = item.getJSONObject("adspaymentcycle");
                if (adspaymentcycle != null) {
                    update.setIsPrepay(false);
                    JSONArray adspaymentcycleData = adspaymentcycle.getJSONArray("data");
                    if (adspaymentcycleData != null && !adspaymentcycleData.isEmpty()) {
                        JSONObject adspaymentcycleDataItem = adspaymentcycleData.getJSONObject(0);
                        update.setBillingThresholdCurrencyAmount(CommonUtils.divide100(adspaymentcycleDataItem.getBigDecimal("threshold_amount"), null));
                    }
                } else {
                    update.setIsPrepay(true);
                }
                updateList.add(update);
                AdAccountStatusEnum newAccountStatus = AdAccountStatusEnum.getEnum(accountStatus);
                if (!exist.getAccountStatus().equals(newAccountStatus)) {
                    AdAccountDO statusChange = new AdAccountDO();
                    statusChange.setPlatformAdId(platformAdId);
                    statusChange.setAccountStatus(newAccountStatus);
                    AdAccountStatusChangeEvent adAccountStatusChangeEvent = new AdAccountStatusChangeEvent(statusChange);
                    SpringUtil.publishEvent(adAccountStatusChangeEvent);
                }
                // 判断限额是否被清除
                if (accountStatus != 2 && exist.getSpendCap() != null && exist.getSpendCap()
                    .compareTo(new BigDecimal(-1)) != 0) {
                    if (spendCap == null || spendCap.compareTo(BigDecimal.ZERO) == 0) {
                        SpringUtil.publishEvent(new AdAccountStolenEvent(platformAdId, false, true));
                    }
                    adAccountSpentCapCheck(exist, spendCap);
                }
                if (exist.getRealAdtrustDsl() != null && exist.getRealAdtrustDsl()
                    .compareTo(BigDecimal.valueOf(-1)) != 0 && dsl.compareTo(BigDecimal.valueOf(-1)) != 0) {
                    if (dsl.compareTo(exist.getRealAdtrustDsl()) < 0 && dsl.compareTo(new BigDecimal("50")) == 0) {
                        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                            .chatId(telegramChatIdConfig.getAdAccountNotifyChatId())
                            .text("广告户 %s 每日花费限额被降低 %s->%s".formatted(platformAdId, NumberUtil.toStr(exist.getRealAdtrustDsl()), NumberUtil.toStr(dsl)))
                            .build()));
                    }
                }
            }
        }
        this.updateBatchById(updateList);
        // 检测账号是否掉绑
        for (AdAccountDO accountDO : oldAccountList) {
            if (accountDO.getAccountStatus().equals(AdAccountStatusEnum.BANNED)) {
                continue;
            }
            if (!currentAccountList.contains(accountDO.getPlatformAdId())) {
                this.update(Wrappers.<AdAccountDO>lambdaUpdate()
                    .set(AdAccountDO::getParentBrowserNo, "")
                    .eq(AdAccountDO::getPlatformAdId, accountDO.getPlatformAdId()));
                SpringUtil.publishEvent(new AdAccountStolenEvent(accountDO.getPlatformAdId(), true, false));
            }
        }
    }

    @Override
    public Set<String> browserLostCheck(AdAccountBrowserCheckReq req) {
        List<String> exist = this.listObjs(Wrappers.<AdAccountDO>lambdaQuery().select(AdAccountDO::getBrowserNo));
        List<String> list = req.getBrowserNos().contains("\n")
            ? StrUtil.split(req.getBrowserNos(), "\n")
            : StrUtil.split(req.getBrowserNos(), " ");
        Set<String> result = new HashSet<>();
        for (String s : list) {
            if (!exist.contains(s)) {
                result.add(s);
            }
        }
        return result;
    }

    @Override
    public void updateAccountStatus(AdAccountUpdateAccountStatusReq req) {
        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getAccountStatus, req.getAccountStatus())
            .in(AdAccountDO::getId, req.getIds()));
    }

    @Override
    public void updateKeepStatus(AdAccountUpdateKeepStatusReq req) {
        if (req.getKeepStatus().equals(AdAccountKeepStatusEnum.SUCCESS)) {
            throw new BusinessException("暂不支持该状态的批量修改");
        }
        List<AdAccountDO> list = this.list(Wrappers.<AdAccountDO>lambdaQuery()
            .notIn(AdAccountDO::getKeepStatus, List.of(AdAccountKeepStatusEnum.SUCCESS, AdAccountKeepStatusEnum.AUTHORIZED))
            .in(AdAccountDO::getId, req.getIds()));
        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getKeepStatus, req.getKeepStatus())
            .notIn(AdAccountDO::getKeepStatus, List.of(AdAccountKeepStatusEnum.SUCCESS, AdAccountKeepStatusEnum.AUTHORIZED))
            .in(AdAccountDO::getId, req.getIds()));
        List<AdAccountDO> updateList = new ArrayList<>();
        if (req.getKeepStatus().equals(AdAccountKeepStatusEnum.FAIL)) {
            for (AdAccountDO accountDO : list) {
                AdAccountDO update = new AdAccountDO();
                update.setId(accountDO.getId());
                update.setKeepStatus(req.getKeepStatus());
                updateList.add(update);
                threadPoolExecutor.execute(() -> {
                    this.withdrawAllCards(accountDO.getPlatformAdId());
                    this.updateCardStatus(accountDO.getPlatformAdId(), false);
                });
            }
        }
        this.updateBatchById(updateList);
    }

    @Override
    public void updateTag(AdAccountUpdateTagReq req) {
        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getTag, req.getTag())
            .in(AdAccountDO::getId, req.getIds()));
    }

    @Override
    public List<Map<String, Integer>> dailyData(LocalDate date) {
        Map<String, List<AdAccountDO>> listMap = list(new LambdaQueryWrapper<AdAccountDO>().eq(AdAccountDO::getAccountStatus, AdAccountStatusEnum.NORMAL.getValue())
            .likeRight(BaseDO::getCreateTime, date)).stream()
            .filter(adAccount -> adAccount.getTimezone() != null)
            .collect(Collectors.groupingBy(AdAccountDO::getTimezone));
        return listMap.entrySet().stream().map(s -> {
            Map<String, Integer> map = new HashMap<>();
            map.put(s.getKey(), s.getValue().size());
            return map;
        }).toList();
    }

    @Override
    public void updateRemark(AdAccountUpdateRemarkReq req) {
        if (StringUtils.isBlank(req.getRemark())) {
            return;
        }
        String remark = req.getRemark() + ";";

        baseMapper.updateRemark(remark, req.getIds());
    }

    @Override
    public void updateBm1Browser(AdAccountUpdateBm1Req req) {
        List<String> list = req.getAdIds().contains("\n")
            ? StrUtil.split(req.getAdIds(), "\n")
            : StrUtil.split(req.getAdIds(), " ");
        String bm1Browser = StringUtils.isBlank(req.getBm1Browser()) ? "" : req.getBm1Browser();
        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getBm1Browser, bm1Browser)
            .in(AdAccountDO::getPlatformAdId, list));
    }

    @Override
    public void updateBm1Id(AdAccountUpdateBm1Req req) {
        List<String> list = req.getAdIds().contains("\n")
            ? StrUtil.split(req.getAdIds(), "\n")
            : StrUtil.split(req.getAdIds(), " ");
        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getBm1Id, req.getBm1Id())
            .in(AdAccountDO::getPlatformAdId, list));
    }

    @Override
    public PageResp<AdAccountResp> pageInactiveAccounts(InactiveAccountAnalyzeQuery query) {
        Page<AdAccountResp> page = new Page<>(query.getPage(), query.getSize());
        IPage<AdAccountResp> pageResult = baseMapper.selectInactiveAccounts(page, query);
        return PageResp.build(pageResult, AdAccountResp.class);
    }

    @Override
    public Long countInactiveAccounts(InactiveAccountAnalyzeQuery query) {
        return baseMapper.selectCountInactiveAccounts(query);
    }

    @Override
    public Integer getTotalInventory(Integer[] saleStatuses) {
        return this.baseMapper.getTotalInventory(saleStatuses);
    }

    private void addBalance(Long adAccountId, BigDecimal amount) {
        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .setSql("balance = balance + " + amount)
            .eq(AdAccountDO::getId, adAccountId));
    }

    private void reduceBalance(Long adAccountId, BigDecimal amount) {
        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .setSql("balance = balance - " + amount)
            .eq(AdAccountDO::getId, adAccountId));
    }

    private List<AdAccountCardDO> createCard(String platformAdId, String cardInfo, Boolean isDefault) {
        List<AdAccountCardDO> list = new ArrayList<>();
        if (cardInfo.contains("换绑")) {
            String[] cards = cardInfo.replaceAll(" ", "").split("换绑");
            for (int i = 0; i < cards.length; i++) {
                Matcher matcher = pattern.matcher(cards[i]);
                if (matcher.find()) {
                    AdAccountCardDO card = new AdAccountCardDO();
                    card.setPlatformAdId(platformAdId);
                    card.setFullCardNumber(matcher.group());
                    card.setIsDefault(isDefault);
                    if (i == 0) {
                        card.setIsRemove(true);
                        card.setIsDefault(false);
                    }
                    list.add(card);
                }
            }
        } else {
            Matcher matcher = pattern.matcher(cardInfo);
            if (matcher.find()) {
                AdAccountCardDO card = new AdAccountCardDO();
                card.setPlatformAdId(platformAdId);
                card.setFullCardNumber(matcher.group());
                card.setIsDefault(isDefault);
                list.add(card);
            }
        }

        return list;
    }

    @Override
    public List<AdAccountKeepStatusStatResp> getKeepStatusStat(AdAccountQuery query) {
        if (null == query.getStartTime() || null == query.getEndTime()) {
            throw new BusinessException("请选择统计时间");
        }

        return baseMapper.selectKeepStatusStat(query);
    }

    @Override
    public BigDecimal getClearAmount(String platformAdId) {
        AdAccountDO account = this.getByPlatformAdId(platformAdId);
        if (account.getUpdateTime().isBefore(LocalDateTime.now().minusHours(1))) {
            throw new BusinessException("广告户近期内数据未更新，请手动填写");
        }
        return account.getSpendCap().subtract(account.getAmountSpent());
    }

    @Override
    public void updateBM5(AdAccountUpdateBMReq req) {

    }

    @Override
    public List<AdAccountInventoryResp> getInventory(boolean before22hourFilter) {
        return baseMapper.getAdAccountInventory(before22hourFilter);
    }

    @Override
    public List<AdAccountInventoryResp> getExternalAccountInventory() {
        return baseMapper.getExternalAccountInventory();
    }

    @Override
    public List<AdAccountInventoryResp> getKeepSuccessInventory() {
        return this.baseMapper.getAdAccountKeepSuccessInventory();
    }

    @Override
    public void calcCost() {

    }

    @Override
    public void updateSpent(AdAccountSpentUpdateReq req) {
        AdAccountDO update = new AdAccountDO();
        BeanUtil.copyProperties(req, update);
        this.updateById(update);
    }

    @Override
    public List<AdAccountResp> selectInventoryList() {
        return this.baseMapper.selectInventoryList();
    }

    @Override
    public List<AdAccountDO> selectWaitSaleAdAccountList(String timeZone,
                                                         Integer useCleanBm5,
                                                         Boolean isLowLimit,
                                                         Boolean requireVo,
                                                         LocalDateTime localDateTime,
                                                         Integer bmType) {
        return baseMapper.selectWaitSaleAdAccountList(timeZone, useCleanBm5, isLowLimit, requireVo, localDateTime, bmType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSaleStatus(AdAccountUpdateSaleStatusReq req) {
        List<AdAccountSaleStatusEnum> invalidList = List.of(AdAccountSaleStatusEnum.SALEING, AdAccountSaleStatusEnum.SALT, AdAccountSaleStatusEnum.RECYCLE);
        CheckUtils.throwIf(invalidList.contains(req.getSaleStatus()), "该状态不支持批量修改");
        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getSaleStatus, req.getSaleStatus())
            .in(AdAccountDO::getId, req.getIds())
            .notIn(AdAccountDO::getSaleStatus, invalidList));
    }

    @Override
    public List<AdAccountSpentResp> amountSpentList(String adAccountId) {

        List<AdAccountBrowserLogDO> list = adAccountBrowserLogService.list(new LambdaQueryWrapper<AdAccountBrowserLogDO>().eq(AdAccountBrowserLogDO::getPlatformAdId, adAccountId)
            .eq(AdAccountBrowserLogDO::getLabel, "账单户设置状态查询")
            .orderByDesc(AdAccountBrowserLogDO::getCreateTime)
            .last("limit 20"));

        List<AdAccountSpentResp> respList = new ArrayList<>();
        for (AdAccountBrowserLogDO adAccountBrowserLogDO : list) {
            AdAccountSpentResp adAccountSpentResp = new AdAccountSpentResp();
            adAccountSpentResp.setDate(adAccountBrowserLogDO.getCreateTime());
            if (StringUtils.isNoneBlank(adAccountBrowserLogDO.getData())) {
                JSONObject json = JSONObject.parse(adAccountBrowserLogDO.getData());
                adAccountSpentResp.setSpent(json.getBigDecimal("amountSpent"));
            }
            respList.add(adAccountSpentResp);
        }

        return respList.stream()
            .sorted(Comparator.comparing(AdAccountSpentResp::getDate).reversed())
            .collect(Collectors.toMap(resp -> resp.getDate()
                .toLocalDate(), resp -> resp, (existing, replacement) -> existing, LinkedHashMap::new))
            .values()
            .stream()
            .toList();
    }

    @Override
    public List<AdAccountDO> listWaitKeepingAccount(BigDecimal minSpent) {
        return this.list(Wrappers.<AdAccountDO>lambdaQuery()
            .select(AdAccountDO::getPlatformAdId, AdAccountDO::getBrowserNo)
            .in(AdAccountDO::getKeepStatus, List.of(AdAccountKeepStatusEnum.PROCESS, AdAccountKeepStatusEnum.WAIT_SPENT))
            .ge(AdAccountDO::getAmountSpent, minSpent));
    }

    @Override
    public void checkAdAccountNurturingStatus(IdsReq req) {
        List<AdAccountDO> list = this.list(Wrappers.<AdAccountDO>lambdaQuery()
            .select(AdAccountDO::getId, AdAccountDO::getPlatformAdId, AdAccountDO::getPlatformAccountId, AdAccountDO::getHeaders, AdAccountDO::getAmountSpent)
            .in(CollUtil.isNotEmpty(req.getIds()), AdAccountDO::getId, req.getIds())
            .in(AdAccountDO::getKeepStatus, List.of(AdAccountKeepStatusEnum.PROCESS, AdAccountKeepStatusEnum.WAIT_SPENT))
            .isNotNull(AdAccountDO::getHeaders));
        if (list.isEmpty()) {
            return;
        }
        int minSpent = 4;
        for (AdAccountDO accountDO : list) {
            if (StringUtils.isBlank(accountDO.getPlatformAccountId())) {
                continue;
            }
            if (StringUtils.isBlank(accountDO.getHeaders())) {
                continue;
            }
            FbAccountDO fbAccount = fbAccountService.getFbAccountByFbAccountId(accountDO.getPlatformAccountId());
            if (fbAccount == null) {
                continue;
            }
            if (StringUtils.isBlank(fbAccount.getFormatProxy())) {
                continue;
            }
            adSpentCheckPoolExecutor.execute(() -> {
                String result = RabbitUtils.checkAdAccountNurturingStatus(accountDO.getPlatformAdId(), fbAccount.getFormatProxy(), minSpent, 20, accountDO.getHeaders());
                if (StringUtils.isNotBlank(result)) {
                    JSONObject json = JSONObject.parseObject(result);
                    String code = json.getString("code");
                    if ("0".equals(code)) {
                        JSONObject data = json.getJSONObject("data");
                        JSONObject adAccount = data.getJSONObject("adAccount");
                        if (adAccount.containsKey("error")) {
                            return;
                        }
                        BigDecimal amountSpent = CommonUtils.divide100(adAccount.getBigDecimal("amount_spent"), BigDecimal.ZERO);
                        BigDecimal totalSpent = Optional.ofNullable(adAccount.getBigDecimal("all_amount_spent"))
                            .orElse(new BigDecimal(0));
                        log.info("【广告户养户状态同步】[{}]消耗同步成功：当前消耗：{}，总消耗：{}", accountDO.getPlatformAdId(), amountSpent, totalSpent);
                        AdAccountDO update = new AdAccountDO();
                        update.setId(accountDO.getId());
                        update.setSpendCap(CommonUtils.divide100(adAccount.getBigDecimal("spend_cap"), BigDecimal.ZERO));
                        update.setAmountSpent(amountSpent);
                        update.setBalance(CommonUtils.divide100(adAccount.getBigDecimal("balance"), BigDecimal.ZERO));
                        update.setTotalSpent(totalSpent);
                        if (totalSpent.compareTo(new BigDecimal(minSpent)) >= 0) {
                            boolean hasCampaignOpen = false;
                            update.setKeepStatus(AdAccountKeepStatusEnum.FINISH);
                            // 检查广告系列是否关闭
                            JSONArray campaigns = data.getJSONArray("campaigns");
                            for (int i = 0; i < campaigns.size(); i++) {
                                JSONObject campaign = campaigns.getJSONObject(i);
                                String status = campaign.getString("status");
                                if (status.equals("ACTIVE")) {
                                    hasCampaignOpen = true;
                                    break;
                                }
                            }
                            if (hasCampaignOpen) {
                                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                                    .chatId(telegramChatIdConfig.getProductionChatId())
                                    .text("广告户 %s 养号广告系列未关闭，请及时处理".formatted(accountDO.getPlatformAdId()))
                                    .build()));
                            }
                        }
                        this.updateById(update);
                        if (amountSpent.compareTo(accountDO.getAmountSpent()) != 0) {
                            AdAccountBrowserLogDO adAccountBrowserLogDO = new AdAccountBrowserLogDO();
                            adAccountBrowserLogDO.setName("BillingHubPaymentSettingsViewQuery");
                            adAccountBrowserLogDO.setData(JSON.toJSONString(update));
                            adAccountBrowserLogDO.setLabel("账单户设置状态查询");
                            adAccountBrowserLogDO.setOpsTime(System.currentTimeMillis());
                            adAccountBrowserLogDO.setPlatformAccountId(accountDO.getPlatformAccountId());
                            adAccountBrowserLogDO.setCreateUser(1L);
                            adAccountBrowserLogDO.setPlatformAdId(accountDO.getPlatformAdId());
                            adAccountBrowserLogService.save(adAccountBrowserLogDO);
                        }
                    } else {
                        log.info("【广告户养户状态同步】[{}]数据同步错误:{}", accountDO.getPlatformAdId(), result);
                        this.update(Wrappers.<AdAccountDO>lambdaUpdate()
                            .set(AdAccountDO::getHeaders, null)
                            .eq(AdAccountDO::getId, accountDO.getId()));
                    }
                }
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUnusableReason(AdAccountUpdateUnusableReasonReq req) {
        for (Long id : req.getIds()) {
            AdAccountDO old = baseMapper.selectById(id);
            CheckUtils.throwIfNull(old, "广告户不存在");

            AdAccountDO update = new AdAccountDO();
            update.setId(id);
            update.setUnusableReason(req.getUnusableReason());
            update.setUsable(false);
            baseMapper.updateById(update);
        }

    }

    @Override
    public CardDO openCard(CardOpenReq req) {
        try {
            AdAccountDO accountDO = this.getByPlatformAdId(req.getPlatformAdId());
            boolean cardBinLimitFlag = tagRelationService.exists(Wrappers.<TagRelationDO>lambdaQuery()
                .eq(TagRelationDO::getRelationId, accountDO.getId())
                .eq(TagRelationDO::getType, 1)
                .eq(TagRelationDO::getTagId, 732283752029357312L));
            if (cardBinLimitFlag) {
                // 只允许开相同卡头的卡
                AdAccountCardDO adAccountCardDO = adAccountCardService.getDefaultCard(accountDO.getPlatformAdId());
                String newCardBin = req.getCardBin().replace("CBIN", "");
                if (!adAccountCardDO.getFullCardNumber().startsWith(newCardBin)) {
                    throw new BusinessException("只允许开与卡片 %s 相同的卡头".formatted(adAccountCardDO.getFullCardNumber()));
                }
            }
            CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(req.getPlatform());
            JSONObject openCardReq = JSONObject.from(req);
            //光子易需要设置持卡人ID以及requestId
            if (CardPlatformEnum.PHOTON_PAY.equals(req.getPlatform())) {
                if (StrUtil.isBlank(req.getCardScheme())) {
                    throw new BusinessException("卡组织不能为空");
                }

                UserDO user = userService.getById(StpUtil.getLoginIdAsLong());
                if (null == user || StringUtils.isBlank(user.getGzyCardHolderId())) {
                    throw new BusinessException("没有配置卡台的用卡人");
                }

                String requestId = "GZY_" + IdUtil.getSnowflakeNextIdStr();
                openCardReq.put("requestId", requestId);
                openCardReq.put("cardHolderId", user.getGzyCardHolderId());
            }

            CardDO cardDO = cardOpsStrategy.openCard(openCardReq);
            if (null != cardDO) {
                cardDO.setCardName(RandomEnglishNameGenerator.generateRandomName());
                cardDO.setCreateUser(UserContextHolder.getUserId());
                cardService.save(cardDO);
            }

            return cardDO;
        } catch (Exception e) {
            throw new BusinessException("开卡失败：" + e.getMessage());
        }
    }

    @Override
    public void recoverUsable(String platformAdId) {
        AdAccountDO accountDO = this.getByPlatformAdId(platformAdId);
        boolean flag = this.update(Wrappers.<AdAccountDO>lambdaUpdate()
            .set(AdAccountDO::getUnusableReason, null)
            .set(AdAccountDO::getBanTime, null)
            .set(AdAccountDO::getAccountStatus, AdAccountStatusEnum.NORMAL)
            .set(AdAccountDO::getUsable, true)
            .eq(AdAccountDO::getId, accountDO.getId())
            .eq(AdAccountDO::getUsable, false));
        if (flag) {
            // 判断是否需要补充金额
            if (!accountDO.getClearStatus().equals(AdAccountClearStatusEnum.WAIT) || !accountDO.getSaleStatus()
                .equals(AdAccountSaleStatusEnum.SALT)) {
                return;
            }
            BigDecimal fbBalance = accountDO.getSpendCap().subtract(accountDO.getAmountSpent());
            if (fbBalance.compareTo(BigDecimal.ZERO) > 0) {
                this.updateCardStatus(platformAdId, true);
                this.rechargeMasterCard(platformAdId, fbBalance, false);
            }
        }
    }

    @Override
    public Map<String, String> getBrowserNoByPlatformAdIds(List<String> platformAdIds) {
        if (CollUtil.isEmpty(platformAdIds)) {
            return new HashMap<>();
        }
        List<AdAccountBrowserResp> browserList = baseMapper.selectBrowserNoByPlatformAdIds(platformAdIds);
        return browserList.stream()
            .collect(Collectors.toMap(AdAccountBrowserResp::getPlatformAdId, AdAccountBrowserResp::getBrowserNo, (existing, replacement) -> existing));
    }

    @Override
    public void rechargeLastBalance(String platformAdId) {
        AdAccountDO adAccount = this.getByPlatformAdId(platformAdId);
        AdAccountCardDO adAccountCard = adAccountCardService.getDefaultCard(adAccount.getPlatformAdId());
        CheckUtils.throwIfNull(adAccountCard, "未找到默认卡片设置");
        CardDO cardDO = cardService.getByCardNumber(adAccountCard.getFullCardNumber(), false);
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(adAccountCard.getPlatform());
        BigDecimal cardBalance = cardOpsStrategy.getCardBalance(cardDO);
        if (cardBalance.compareTo(adAccount.getBalance()) < 0) {
            BigDecimal addBalance = adAccount.getBalance().subtract(cardBalance);
            try {
                cardOpsStrategy.rechargeCard(cardDO, addBalance);
            } catch (Exception e) {
                throw new BusinessException(e.getMessage());
            }
        }
    }

    /**
     * fb限额检测
     *
     * @param adAccount
     * @param spentCap
     */
    private void adAccountSpentCapCheck(AdAccountDO adAccount, BigDecimal spentCap) {
        if (adAccount.getSpendCap() == null || adAccount.getSpendCap().compareTo(new BigDecimal(-1)) == 0) {
            return;
        }
        if (adAccount.getIsPrepay()) {
            return;
        }
        if (spentCap == null || spentCap.compareTo(new BigDecimal(10)) <= 0) {
            return;
        }
        if (!adAccount.getSaleStatus().equals(AdAccountSaleStatusEnum.SALT) || !adAccount.getAccountStatus()
            .equals(AdAccountStatusEnum.NORMAL) || !adAccount.getUsable()) {
            return;
        }
        if (adAccount.getSpendCap().compareTo(spentCap) == 0) {
            return;
        }
        if (adAccount.getRemark().contains("买断")) {
            return;
        }
        threadPoolExecutor.execute(() -> {
            BigDecimal rechargeAmount = this.baseMapper.getRechargeAmountAfterClear(null, adAccount.getPlatformAdId());
            log.info("【广告户限额检测】{} 旧限额：${}，最新限额：${}，当前充值金额：${}", adAccount.getPlatformAdId(), adAccount.getSpendCap(), spentCap, rechargeAmount);
            if (rechargeAmount.compareTo(spentCap) != 0) {
                String text = BotUtils.createSpentCapMismatchMessage(adAccount.getPlatformAdId(), spentCap, rechargeAmount);
                SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                    .chatId(telegramChatIdConfig.getMonitorChatId())
                    .parseMode(ParseMode.MARKDOWNV2)
                    .text(text)
                    .build()));
            }
        });
    }
}