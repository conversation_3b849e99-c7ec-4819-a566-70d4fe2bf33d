/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.admin.biz.mapper.CampaignInsightMapper;
import top.continew.admin.biz.model.entity.CampaignInsightDO;
import top.continew.admin.biz.service.CampaignInsightService;

/**
 * 广告户系列消耗业务实现
 *
 * <AUTHOR>
 * @since 2025/01/03 15:08
 */
@Service
@RequiredArgsConstructor
public class CampaignInsightServiceImpl extends ServiceImpl<CampaignInsightMapper, CampaignInsightDO> implements CampaignInsightService {
}