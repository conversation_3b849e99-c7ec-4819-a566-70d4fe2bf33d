/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardTransactionTypeEnum;
import top.continew.admin.biz.enums.GzyCardHolderEnum;
import top.continew.admin.biz.event.CardTransactionEvent;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.CardTransactionMapper;
import top.continew.admin.biz.model.entity.CardTransactionDO;
import top.continew.admin.biz.model.query.CardTransactionQuery;
import top.continew.admin.biz.model.query.CardTransactionStatisticsQuery;
import top.continew.admin.biz.model.req.CardTransactionRemarkReq;
import top.continew.admin.biz.model.req.CardTransactionReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.biz.service.CardTransactionService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 卡片交易流水业务实现
 *
 * <AUTHOR>
 * @since 2024/12/31 21:57
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CardTransactionServiceImpl extends BaseServiceImpl<CardTransactionMapper, CardTransactionDO, CardTransactionResp, CardTransactionDetailResp, CardTransactionQuery, CardTransactionReq> implements CardTransactionService {

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    private final CardService cardService;

    @Override
    public List<CardTransactionDO> listByPlatform(Integer platform, LocalDateTime start, LocalDateTime end) {
        return this.list(Wrappers.<CardTransactionDO>lambdaQuery()
            .eq(CardTransactionDO::getPlatform, platform)
            .between(ObjectUtils.allNotNull(start, end), CardTransactionDO::getTransTime, start, end));
    }

    @Override
    public void syncData(LocalDateTime start, LocalDateTime end) {
        for (CardPlatformEnum value : CardPlatformEnum.values()) {
            if (!value.isEnable()) {
                continue;
            }
            this.syncData(value, start, end);
        }
    }

    @Override
    @Async
    public void syncData(CardPlatformEnum platform, LocalDateTime start, LocalDateTime end) {
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(platform);
        if (cardOpsStrategy != null) {
            List<CardTransactionDO> saveList = new ArrayList<>();
            List<CardTransactionDO> updateList = new ArrayList<>();
            List<CardTransactionDO> list = cardOpsStrategy.getCardTransactionList(start, end, null);
            //TODO amz 要怎么处理
            if (platform.equals(CardPlatformEnum.CARD_VP)) {
                if (start != null) {
                    start = start.minusHours(15);
                }
                if (end != null) {
                    end = end.minusHours(15);
                }
            } else if (platform.equals(CardPlatformEnum.PHOTON_PAY)) {
                if (start != null) {
                    start = start.minusHours(8);
                }
                if (end != null) {
                    end = end.minusHours(8);
                }
            }
            List<CardTransactionDO> existList = this.listByPlatform(platform.getValue(), start, end);
            for (CardTransactionDO item : list) {
                boolean isCardVp = platform.equals(CardPlatformEnum.CARD_VP);
                CardTransactionDO exist = existList.stream()
                    .filter(v -> isCardVp
                        ? v.getTransactionId()
                        .equalsIgnoreCase(item.getTransactionId()) && v.getCardNumber()
                        .equalsIgnoreCase(item.getCardNumber())
                        : v.getTransactionId().equalsIgnoreCase(item.getTransactionId()))
                    .findFirst()
                    .orElse(null);
                LocalDateTime statTime = item.getChinaTime();
                if (!isCardVp && StringUtils.isNotBlank(item.getOriginTransactionId())) {
                    if (item.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION_BACK) || item.getTransType()
                        .equals(CardTransactionTypeEnum.REFUND)) {
                        CardTransactionDO originRecord = this.getOne(Wrappers.<CardTransactionDO>lambdaQuery()
                            .eq(CardTransactionDO::getTransactionId, item.getOriginTransactionId()));
                        if (originRecord != null) {
                            statTime = originRecord.getChinaTime();
                        }
                    }
                }
                item.setStatTime(statTime);
                if (exist == null) {
                    log.info("【{}】{}交易新增，状态{}，类型{}，金额{}，交易时间{}", platform.getDescription(), item.getTransactionId(), item.getTransStatus(), item.getOriginTransType(), item.getTransAmount(), item.getTransTime());
                    saveList.add(item);
                } else {
                    // 判断交易数据是否有变化
                    boolean flag = false;
                    CardTransactionDO update = new CardTransactionDO();
                    update.setId(exist.getId());
                    if (!exist.getTransStatus().equals(item.getTransStatus())) {
                        flag = true;
                        update.setTransStatus(item.getTransStatus());
                    }
                    if (!exist.getOriginTransType().equals(item.getOriginTransType())) {
                        flag = true;
                        update.setOriginTransType(item.getOriginTransType());
                        update.setTransType(item.getTransType());
                    }
                    if (exist.getTransAmount().compareTo(item.getTransAmount()) != 0) {
                        flag = true;
                        update.setTransAmount(item.getTransAmount());
                    }
                    if (flag) {
                        log.info("【{}】{}交易更新，状态{}->{}，类型{}->{}，金额{}->{}，交易时间{}->{}", platform.getDescription(), item.getTransactionId(), exist.getTransStatus(), item.getTransStatus(), exist.getOriginTransType(), item.getOriginTransType(), exist.getTransAmount(), item.getTransAmount(), exist.getTransTime(), item.getTransTime());
                        updateList.add(update);
                    }
                }
            }
            this.saveBatch(saveList, 2000);
            this.updateBatchById(updateList, 2000);
            log.info("【{}】交易数据入库完成", platform.getDescription());
        }
    }

    @Override
    public BigDecimal sumAmountByDateRange(String platformAdId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.sumAmountByDateRange(platformAdId, startTime, endTime);
    }

    @Override
    public void remark(Long id, CardTransactionRemarkReq req) {
        this.update(Wrappers.<CardTransactionDO>lambdaUpdate()
            .set(CardTransactionDO::getRemark, req.getRemark())
            .eq(CardTransactionDO::getId, id));
    }

    @Override
    public void syncClearTransactionData() {
        CardPlatformEnum platform = CardPlatformEnum.CARD_VP;
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(platform);
        List<CardTransactionDO> updateList = new ArrayList<>();
        List<CardTransactionDO> existList = this.listByPlatform(platform.getValue(), null, null);
        List<CardTransactionDO> list = cardOpsStrategy.getCardTransactionList(null, null, "CLEARED");
        for (CardTransactionDO item : list) {
            existList.stream()
                .filter(v -> v.getTransactionId().equals(item.getTransactionId()) && v.getCardNumber()
                    .equals(item.getCardNumber()))
                .findFirst()
                .ifPresent(exist -> {
                    CardTransactionDO update = new CardTransactionDO();
                    update.setId(exist.getId());
                    update.setOriginTransType(item.getOriginTransType());
                    update.setTransType(item.getTransType());
                    update.setTransAmount(item.getTransAmount());
                    update.setTransStatus(item.getTransStatus());
                    update.setClearAmount(exist.getTransAmount());
                    updateList.add(update);
                });
        }
        this.updateBatchById(updateList);
    }

    @Override
    public void convertCardTransactionTime() {

    }

    @Override
    public void syncCardVpByCallback(JSONObject data) {
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(CardPlatformEnum.CARD_VP);
        List<CardTransactionDO> saveList = new ArrayList<>();
        List<CardTransactionDO> updateList = new ArrayList<>();
        List<CardTransactionDO> list = cardOpsStrategy.convertCardTransactionList(data.getJSONArray("transactions"));
        String cardNumber = list.get(0).getCardNumber();
        List<CardTransactionDO> existList = this.list(Wrappers.<CardTransactionDO>lambdaQuery()
            .eq(CardTransactionDO::getCardNumber, cardNumber));
        for (CardTransactionDO item : list) {
            CardTransactionDO exist = existList.stream()
                .filter(v -> v.getTransactionId().equalsIgnoreCase(item.getTransactionId()))
                .findFirst()
                .orElse(null);
            item.setStatTime(item.getChinaTime());
            if (exist == null) {
                log.info("【CARD_VP】{}交易推送，状态{}，类型{}，金额{}，交易时间{}", item.getTransactionId(), item.getTransStatus(), item.getOriginTransType(), item.getTransAmount(), item.getTransTime());
                saveList.add(item);
                CardTransactionEvent cardTransactionEvent = new CardTransactionEvent(item);
                SpringUtil.publishEvent(cardTransactionEvent);
            } else {
                // 判断交易数据是否有变化
                boolean flag = false;
                CardTransactionDO update = new CardTransactionDO();
                update.setId(exist.getId());
                if (!exist.getTransStatus().equals(item.getTransStatus())) {
                    flag = true;
                    update.setTransStatus(item.getTransStatus());
                }
                if (!exist.getOriginTransType().equals(item.getOriginTransType())) {
                    flag = true;
                    update.setOriginTransType(item.getOriginTransType());
                    update.setTransType(item.getTransType());
                }
                if (exist.getTransAmount().compareTo(item.getTransAmount()) != 0) {
                    flag = true;
                    update.setTransAmount(item.getTransAmount());
                }
                if (!exist.getTransTime().equals(item.getTransTime())) {
                    flag = true;
                    update.setTransTime(item.getTransTime());
                    update.setChinaTime(item.getChinaTime());
                }
                if (flag) {
                    log.info("【CARD_VP】{}交易推送，状态{}->{}，类型{}->{}，金额{}->{}，交易时间{}->{}", item.getTransactionId(), exist.getTransStatus(), item.getTransStatus(), exist.getOriginTransType(), item.getOriginTransType(), exist.getTransAmount(), item.getTransAmount(), exist.getTransTime(), item.getTransTime());
                    updateList.add(update);
                }
            }
        }
        this.saveBatch(saveList);
        this.updateBatchById(updateList);
    }

    @Override
    public void updateCardNumber(CardPlatformEnum platform) {
        baseMapper.updateCardNumber(platform.getValue());
    }

    @Override
    @Async
    public void handleGzyTransaction(JSONArray jsonArray) {
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(CardPlatformEnum.PHOTON_PAY);
        List<CardTransactionDO> list = cardOpsStrategy.convertCardTransactionList(jsonArray);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<CardTransactionDO> saveList = new ArrayList<>();
        List<CardTransactionDO> updateList = new ArrayList<>();

        for (CardTransactionDO row : list) {
            List<CardTransactionDO> existRecords = baseMapper.selectList(new LambdaQueryWrapper<CardTransactionDO>().eq(CardTransactionDO::getPlatform, CardPlatformEnum.PHOTON_PAY.getValue())
                .eq(CardTransactionDO::getTransactionId, row.getTransactionId())
                .last("limit 1"));
            LocalDateTime statTime = row.getChinaTime();
            if (row.getTransType().equals(CardTransactionTypeEnum.AUTHORIZATION_BACK) || row.getTransType()
                .equals(CardTransactionTypeEnum.REFUND)) {
                CardTransactionDO originRecord = this.getOne(Wrappers.<CardTransactionDO>lambdaQuery()
                    .eq(CardTransactionDO::getTransactionId, row.getOriginTransactionId()));
                if (originRecord != null) {
                    statTime = originRecord.getChinaTime();
                }
            }
            row.setStatTime(statTime);
            if (CollUtil.isEmpty(existRecords)) {
                //配置卡号
                if (StrUtil.isNotBlank(row.getPlatformCardId())) {
                    String cardNumber = cardService.getCardNumber(CardPlatformEnum.PHOTON_PAY, row.getPlatformCardId());
                    if (StrUtil.isNotBlank(cardNumber)) {
                        row.setCardNumber(cardNumber);
                        CardTransactionEvent cardTransactionEvent = new CardTransactionEvent(row);
                        SpringUtil.publishEvent(cardTransactionEvent);
                    }
                }
                log.info("【光子易】{}交易推送，状态{}，类型{}，金额{}，交易时间{}", row.getTransactionId(), row.getTransStatus(), row.getOriginTransType(), row.getTransAmount(), row.getTransTime());
                saveList.add(row);

            } else {
                //光子易的状态包括："pending" "authorized" "succeed" "failed" "void" "processing"
                CardTransactionDO existRecord = existRecords.get(0);
                log.info("【光子易】{}交易推送，状态{}->{}，类型{}->{}，金额{}->{}，交易时间{}->{}", row.getTransactionId(), existRecord.getTransStatus(), row.getTransStatus(), existRecord.getOriginTransType(), row.getOriginTransType(), existRecord.getTransAmount(), row.getTransAmount(), existRecord.getTransTime(), row.getTransTime());
                if (null != existRecord.getOriginTransType() && existRecord.getOriginTransType()
                    .equals(row.getOriginTransType())) {
                    continue;
                }
                String oldOriginStatus = existRecord.getOriginTransType().split(",")[1];
                String newOriginStatus = row.getOriginTransType().split(",")[1];
                //如果是succeed，后面又接收到了authorized/processing/pending，则不变化
                if (oldOriginStatus.equals("succeed") && ArrayUtil.contains(new String[] {"authorized", "processing",
                    "pending"}, newOriginStatus)) {
                    continue;
                }

                CardTransactionDO update = new CardTransactionDO();
                update.setId(existRecord.getId());
                update.setOriginTransType(row.getOriginTransType());
                update.setTransType(row.getTransType());
                update.setTransAmount(row.getTransAmount());
                update.setTransStatus(row.getTransStatus());
                updateList.add(update);
            }
        }

        if (CollUtil.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }

        if (CollUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
    }

    @Override
    @Async
    public void handleAmzTransaction(JSONArray jsonArray) {
        // 只有授权交易的，才会推送
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(CardPlatformEnum.AMZ);
        List<CardTransactionDO> list = cardOpsStrategy.convertCardTransactionList(jsonArray);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<CardTransactionDO> saveList = new ArrayList<>();
        List<CardTransactionDO> updateList = new ArrayList<>();

        for (CardTransactionDO row : list) {
            CardTransactionDO existRecord = baseMapper.selectOne(new LambdaQueryWrapper<CardTransactionDO>().eq(CardTransactionDO::getPlatform, CardPlatformEnum.AMZ.getValue())
                .eq(CardTransactionDO::getTransactionId, row.getTransactionId())
                .last("limit 1"));
            LocalDateTime statTime = row.getChinaTime();
            row.setStatTime(statTime);
            if (existRecord == null) {
                //配置卡号
                if (StrUtil.isNotBlank(row.getPlatformCardId())) {
                    String cardNumber = cardService.getCardNumber(CardPlatformEnum.AMZ, row.getPlatformCardId());
                    if (StrUtil.isNotBlank(cardNumber)) {
                        row.setCardNumber(cardNumber);
                        CardTransactionEvent cardTransactionEvent = new CardTransactionEvent(row);
                        SpringUtil.publishEvent(cardTransactionEvent);
                    }
                }
                log.info("【AMZ】{}交易推送，状态{}，类型{}，金额{}，交易时间{}", row.getTransactionId(), row.getTransStatus(), row.getOriginTransType(), row.getTransAmount(), row.getTransTime());
                saveList.add(row);

            } else {
                log.info("【AMZ】{}交易推送，状态{}->{}，类型{}->{}，金额{}->{}，交易时间{}->{}", row.getTransactionId(), existRecord.getTransStatus(), row.getTransStatus(), existRecord.getOriginTransType(), row.getOriginTransType(), existRecord.getTransAmount(), row.getTransAmount(), existRecord.getTransTime(), row.getTransTime());
                if (null != existRecord.getOriginTransType() && existRecord.getOriginTransType()
                    .equals(row.getOriginTransType())) {
                    continue;
                }
                //FIXME
                String oldOriginStatus = existRecord.getOriginTransType().split(",")[1];
                String newOriginStatus = row.getOriginTransType().split(",")[1];
                //如果是成功的交易，后面又接收到了其他状态，则不变化
                if (("成功").equals(oldOriginStatus) && !"成功".equals(newOriginStatus)) {
                    continue;
                }

                CardTransactionDO update = new CardTransactionDO();
                update.setId(existRecord.getId());
                update.setOriginTransType(row.getOriginTransType());
                update.setTransType(row.getTransType());
                update.setTransAmount(row.getTransAmount());
                update.setTransStatus(row.getTransStatus());
                updateList.add(update);
            }
        }

        if (CollUtil.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }

        if (CollUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
    }

    @Override
    public void updateAdAccountId(LocalDateTime startTime, LocalDateTime endTime) {
        this.baseMapper.updateAdAccountIdByTimeRange(startTime, endTime);
    }

    @Override
    public void updateCustomerId(LocalDateTime startTime, LocalDateTime endTime) {
        this.baseMapper.updateCustomerIdByTimeRange(startTime, endTime);
    }

    @Override
    public CardTransactionSummaryResp statistics(CardTransactionStatisticsQuery query) {
        // 设置查询时间范围
        LocalDateTime startTime = query.getStartTime();
        LocalDateTime endTime = query.getEndTime();

        // 1. 查询汇总数据
        CardTransactionSummaryResp transactionSummary = baseMapper.selectTransactionSummary(query.getPlatform(), startTime, endTime);
        CardTransactionSummaryResp cardSummary = baseMapper.selectCardSummary(query.getPlatform(), startTime, endTime);

        // 合并汇总数据
        CardTransactionSummaryResp summary = new CardTransactionSummaryResp();
        summary.setTotalCardCount(cardSummary.getTotalCardCount());
        summary.setTotalBalance(cardSummary.getTotalBalance());
        summary.setTotalSpend(transactionSummary.getTotalSpend());
        return summary;
    }

    @Override
    public PageResp<CardTransactionStatByCardholderResp> selectStatByCardholderPage(CardTransactionStatisticsQuery query,
                                                                                    PageQuery pageQuery) {
        IPage<CardTransactionStatByCardholderResp> page = baseMapper.selectStatisticsByCardholderPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query.getPlatform(), query.getStartTime(), query.getEndTime());
        if (CollUtil.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(row -> row.setUsername(GzyCardHolderEnum.getHolderName(row.getUsername())));
        }
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResp<CardTransactionStatByDateResp> selectStatByDatePage(CardTransactionStatisticsQuery query,
                                                                        PageQuery pageQuery) {
        IPage<CardTransactionStatByDateResp> page = baseMapper.selectStatisticsByDatePage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query.getPlatform(), query.getStartTime(), query.getEndTime());
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<CardTransactionStatByDateResp> selectStatByDateList(CardTransactionStatisticsQuery query) {
        return this.baseMapper.selectStatisticsByDateList(query.getPlatform(), query.getStartTime(), query.getEndTime());
    }

    @Override
    public PageResp<CardTransactionStatByTimezoneResp> selectStatByTimezonePage(CardTransactionStatisticsQuery query,
                                                                                PageQuery pageQuery) {
        IPage<CardTransactionStatByTimezoneResp> page = baseMapper.selectStatisticsByTimezonePage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query.getPlatform(), query.getStartTime(), query.getEndTime());
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public BigDecimal getAdAccountCardSpent(Long customerId, String platformAdId, LocalDateTime startTime) {
        return this.baseMapper.getAdAccountCardSpent(customerId, platformAdId, startTime);
    }
}