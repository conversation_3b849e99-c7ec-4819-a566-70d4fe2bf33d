package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.CustomerStatusEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.mapper.AdAccountOrderMapper;
import top.continew.admin.biz.mapper.CardTransactionMapper;
import top.continew.admin.biz.mapper.CustomerDailyStatMapper;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.CustomerDailyStatDO;
import top.continew.admin.biz.model.query.CustomerDailyStatQuery;
import top.continew.admin.biz.model.req.CustomerDailyStatReq;
import top.continew.admin.biz.model.resp.CustomerAdAccountCardSpentResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatByDateResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatDetailResp;
import top.continew.admin.biz.model.resp.CustomerDailyStatResp;
import top.continew.admin.biz.service.CustomerDailyStatService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.core.validation.ValidationUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * 客户每日统计业务实现
 *
 * <AUTHOR>
 * @since 2025/07/17 10:11
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerDailyStatServiceImpl extends BaseServiceImpl<CustomerDailyStatMapper, CustomerDailyStatDO, CustomerDailyStatResp, CustomerDailyStatDetailResp, CustomerDailyStatQuery, CustomerDailyStatReq> implements CustomerDailyStatService {

    private final CustomerService customerService;

    private final AdAccountOrderMapper adAccountOrderMapper;

    private final CardTransactionMapper cardTransactionMapper;

    @Override
    public PageResp<CustomerDailyStatResp> page(CustomerDailyStatQuery query, PageQuery pageQuery) {
        QueryWrapper<CustomerDailyStatDO> queryWrapper = this.buildQueryWrapper(query);
        if (query.getUsedRateLowerThan50percent() != null) {
            if (query.getUsedRateLowerThan50percent()) {
                queryWrapper.lt("(stat.today_used_account / stat.total_normal_account)", 0.5);
            } else {
                queryWrapper.ge("(stat.today_used_account / stat.total_normal_account)", 0.5);
            }
        }
        sort(queryWrapper, pageQuery);
        IPage<CustomerDailyStatResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(CustomerDailyStatQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<CustomerDailyStatDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<CustomerDailyStatDetailResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        return BeanUtil.copyToList(entityList, targetClass);
    }
    @Override
    protected void sort(QueryWrapper<CustomerDailyStatDO> queryWrapper, SortQuery sortQuery) {
        if (sortQuery != null && !sortQuery.getSort().isUnsorted()) {
            Sort sort = sortQuery.getSort();
            Iterator var4 = sort.iterator();

            while(var4.hasNext()) {
                Sort.Order order = (Sort.Order)var4.next();
                String property = order.getProperty();
                queryWrapper.orderBy(true, order.isAscending(), CharSequenceUtil.toUnderlineCase(property));
            }

        }
    }

    @Override
    public void statData(LocalDate date) {
        log.info("开始统计 {} 每日客户数据...", date);
        List<CustomerDO> customerList = customerService.list(Wrappers.<CustomerDO>lambdaQuery()
            .eq(CustomerDO::getType, CustomerTypeEnum.FORMAL)
            .eq(CustomerDO::getStatus, CustomerStatusEnum.NORMAL)
            .eq(CustomerDO::getIsSelfAccount, false));
        List<CustomerDailyStatDO> statList = new ArrayList<>();
        List<CustomerDailyStatResp> statRespList = adAccountOrderMapper.getCustomerDailyStat(date);
        List<CustomerAdAccountCardSpentResp> cardSpentList = cardTransactionMapper.getCustomerAdAccountDailySpent(date);
        for (CustomerDO customerDO : customerList) {
            CustomerDailyStatDO item = new CustomerDailyStatDO();
            item.setStatDate(date);
            item.setCustomerId(customerDO.getId());
            statRespList.stream()
                .filter(v -> v.getCustomerId().equals(customerDO.getId()))
                .findFirst()
                .ifPresent(resp -> BeanUtil.copyProperties(resp, item, "statDate", "customerId"));
            if (item.getTotalFinishAccount() == null || item.getTotalFinishAccount() == 0) {
                continue;
            }
            long todayUsedAccount = cardSpentList.stream()
                .filter(v -> v.getCustomerId().equals(customerDO.getId()) && v.getCardSpent()
                    .compareTo(BigDecimal.TEN) >= 0)
                .count();
            item.setTodayUsedAccount((int)todayUsedAccount);
            BigDecimal cardSpent = cardSpentList.stream()
                .filter(v -> v.getCustomerId().equals(customerDO.getId()) && v.getCardSpent()
                    .compareTo(BigDecimal.TEN) >= 0)
                .map(CustomerAdAccountCardSpentResp::getCardSpent)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setTodayCardSpent(cardSpent);
            statList.add(item);
            log.info("{} 每日数据为：{}", customerDO.getName(), JSON.toJSONString(item));
        }
        this.saveBatch(statList);
    }

    @Override
    public PageResp<CustomerDailyStatByDateResp> selectDailyPage(CustomerDailyStatQuery query, PageQuery pageQuery) {
        QueryWrapper<CustomerDailyStatDO> queryWrapper = this.buildQueryWrapper(query);
        queryWrapper.groupBy("stat_date").orderByDesc("stat_date");
        IPage<CustomerDailyStatByDateResp> page = baseMapper.selectDailyPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<CustomerDailyStatByDateResp> selectDailyList(CustomerDailyStatQuery query) {
        QueryWrapper<CustomerDailyStatDO> queryWrapper = this.buildQueryWrapper(query);
        queryWrapper.groupBy("stat_date").orderByDesc("stat_date");
        return baseMapper.selectDailyList(queryWrapper);
    }
}