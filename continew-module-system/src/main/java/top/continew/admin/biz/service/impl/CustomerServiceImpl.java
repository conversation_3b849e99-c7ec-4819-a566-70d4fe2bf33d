/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.event.CustomerBalanceChangeEvent;
import top.continew.admin.biz.event.CustomerBalanceChangeModel;
import top.continew.admin.biz.excel.CustomerDailyExcel;
import top.continew.admin.biz.mapper.CustomerMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.query.CustomerOrderStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerQuery;
import top.continew.admin.biz.model.query.CustomerStatReportQuery;
import top.continew.admin.biz.model.req.CustomerBalanceChangeReq;
import top.continew.admin.biz.model.req.CustomerOpenReq;
import top.continew.admin.biz.model.req.CustomerReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.CommonUtils;
import top.continew.admin.biz.utils.CustomerHelper;
import top.continew.admin.common.util.SecureUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 客户业务实现
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Service
@RequiredArgsConstructor
public class CustomerServiceImpl extends BaseServiceImpl<CustomerMapper, CustomerDO, CustomerResp, CustomerDetailResp, CustomerQuery, CustomerReq> implements CustomerService {

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final AdAccountBalanceRecordService adAccountBalanceRecordService;

    private final AdAccountService adAccountService;

    private final AdAccountCardService adAccountCardService;

    private final CardService cardService;

    @Override
    public Long changeAmount(Long customerId,
                             BigDecimal amount,
                             CustomerBalanceTypeEnum balanceType,
                             LocalDateTime transactionTime,
                             String remark) {
        return this.changeAmount(customerId, null, amount, balanceType, transactionTime, remark, null, null);
    }

    @Override
    protected void beforeAdd(CustomerReq req) {
        // 判断客户名称是否重复
        LambdaQueryWrapper<CustomerDO> queryWrapper = Wrappers.lambdaQuery(CustomerDO.class)
                .eq(CustomerDO::getName, req.getName());
        if (baseMapper.exists(queryWrapper)) {
            throw new BusinessException("已存在相同名称的客户，请不要重复创建");
        }
    }

    @Override
    protected void beforeUpdate(CustomerReq req, Long id) {
        // 判断客户名称是否重复
        if (null != req.getName()) {
            LambdaQueryWrapper<CustomerDO> queryWrapper = Wrappers.lambdaQuery(CustomerDO.class)
                    .eq(CustomerDO::getName, req.getName())
                    .ne(CustomerDO::getId, id);
            if (baseMapper.exists(queryWrapper)) {
                throw new BusinessException("已存在相同名称的客户，请不要重复创建");
            }
        }
    }

    @Override
    public PageResp<CustomerResp> page(CustomerQuery query, PageQuery pageQuery) {
        // 创建分页对象
        Page<CustomerDO> page = new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize());
        IPage<CustomerDO> resultPage = baseMapper.pageCustomers(page, query);
        // 构建返回结果
        PageResp<CustomerResp> pageResp = PageResp.build(resultPage, this.getListClass());
        // 填充额外信息
        pageResp.getList().forEach(this::fill);

        return pageResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long changeAmount(Long customerId,
                             String platformAdId,
                             BigDecimal amount,
                             CustomerBalanceTypeEnum balanceType,
                             LocalDateTime transactionTime,
                             String remark) {
        return this.changeAmount(customerId, platformAdId, amount, balanceType, transactionTime, remark, null, null);
    }

    private Long changeAmount(Long customerId,
                              String platformAdId,
                              BigDecimal amount,
                              CustomerBalanceTypeEnum balanceType,
                              LocalDateTime transactionTime,
                              String remark,
                              String certificate,
                              Long createUser) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        CustomerBalanceRecordDO record = new CustomerBalanceRecordDO();
        record.setCustomerId(customerId);
        boolean isOut = amount.compareTo(BigDecimal.ZERO) < 0;
        if (isOut) {
            record.setAction(TransactionActionEnum.OUT);
        } else {
            record.setAction(TransactionActionEnum.IN);
        }
        record.setType(balanceType);
        record.setAmount(amount.abs());
        record.setTransTime(transactionTime != null ? transactionTime : LocalDateTime.now());
        record.setRemark(remark);
        record.setPlatformAdId(platformAdId);
        record.setCertificate(certificate);
        if (createUser != null) {
            record.setCreateUser(createUser);
        }
        if (isOut) {
            CustomerHelper.reduceBalance(customerId, amount.abs());
        } else {
            CustomerHelper.addBalance(customerId, amount.abs());
        }
        CustomerDO customer = getById(customerId);
        record.setAfterAmount(customer.getBalance());
        customerBalanceRecordService.save(record);
        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeAmount(Long id, CustomerBalanceChangeReq req) {
        CustomerDO customer = this.getById(id);
        BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(customer.getId());
        CustomerBalanceChangeModel changeModel = null;
        Long recordId;
        if (Objects.equals(req.getAction(), CustomerBalanceTypeEnum.SYSTEM.getValue())) {
            CheckUtils.throwIfNull(req.getFeeHandleMethod(), "请选择手续费扣除方式");
            // 计算充值手续费
            BigDecimal fee;
            if (req.getFeeHandleMethod().equals(RechargeFeeHandleMethodEnum.REDUCE_FROM_RECHARGE_AMOUNT)) {
                fee = CommonUtils.divide100(req.getAmount().multiply(customer.getFeeRatePercent()), null);
            } else if (req.getFeeHandleMethod().equals(RechargeFeeHandleMethodEnum.RECHARGE_INCLUDE_FEE)) {
                BigDecimal onePart = req.getAmount()
                        .divide(customer.getFeeRatePercent().add(BigDecimal.valueOf(100)), 4, RoundingMode.HALF_UP);
                fee = onePart.multiply(customer.getFeeRatePercent()).setScale(2, RoundingMode.HALF_UP);
            } else {
                CheckUtils.throwIfNull(req.getFee(), "请输入手续费");
                fee = req.getFee();
            }
            recordId = this.changeAmount(id, null, req.getAmount(), CustomerBalanceTypeEnum.SYSTEM, req.getTransTime(), req.getRemark(), req.getCertificate(), null);
            this.changeAmount(id, fee.negate(), CustomerBalanceTypeEnum.RECHARGE_FEE, req.getTransTime(), null);
            // 发送额度汇总信息
            changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, req.getAmount(), customer.getBalance(), req.getAmount()
                    .subtract(fee));
        } else if (Objects.equals(req.getAction(), CustomerBalanceTypeEnum.DEDUCE.getValue())) {
            recordId = this.changeAmount(id, req.getAmount()
                    .negate(), CustomerBalanceTypeEnum.DEDUCE, req.getTransTime(), req.getRemark());
            changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, req.getAmount().negate(), customer.getBalance(), req.getAmount()
                    .negate());
        } else if (Objects.equals(req.getAction(), CustomerBalanceTypeEnum.RECHARGE_FEE_REFUND.getValue())) {
            recordId = this.changeAmount(id, req.getAmount()
                    .abs(), CustomerBalanceTypeEnum.RECHARGE_FEE_REFUND, req.getTransTime(), req.getRemark());
            changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, req.getAmount().negate(), customer.getBalance(), req.getAmount()
                    .abs());
        } else {
            // 转移金额
            recordId = this.changeAmount(id, req.getAmount(), CustomerBalanceTypeEnum.MIGRATE, req.getTransTime(), req.getRemark());
            // 发送额度汇总信息
            changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, null, customer.getBalance(), req.getAmount());
        }
        if (req.getTransTime() != null) {
            customerBalanceRecordService.adjustAfterAmount(customer.getId(), recordId);
        }
        SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
    }

    @Override
    public PageResp<CustomerStatReportResp> pageCustomerStatReport(CustomerStatReportQuery query, PageQuery pageQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        IPage<CustomerStatReportResp> page = this.baseMapper.selectCustomerStatReport(new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize()), query.getCustomerId(), start, end, query.getIsSelf(), query.getSettleType(), query.getSortField(), query.getAscSortFlag());
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<CustomerStatReportResp> listCustomerStatReport(CustomerStatReportQuery query, SortQuery sortQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        return this.baseMapper.listCustomerStatReport(query.getCustomerId(), start, end, query.getIsSelf(), query.getSettleType());
    }

    @Override
    public CustomerStatSummaryResp getCustomerStatReportSummary(CustomerStatReportQuery query) {
        CustomerStatSummaryResp customerStatSummaryResp = this.baseMapper.getCustomerStatReportSummary(query);
        List<CustomerDO> customerList = this.list(Wrappers.<CustomerDO>lambdaQuery()
                .eq(query.getIsSelf() != null, CustomerDO::getIsSelfAccount, query.getIsSelf())
                .eq(query.getCustomerId() != null, CustomerDO::getId, query.getCustomerId())
                .eq(query.getSettleType() != null, CustomerDO::getSettleType, query.getSettleType()));

        customerStatSummaryResp.setTotalBalance(customerList.stream()
                .map(CustomerDO::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        return customerStatSummaryResp;
    }

    @Override
    public PageResp<CustomerDailyStatReportResp> pageCustomerDailyStatReport(CustomerStatReportQuery query,
                                                                             PageQuery pageQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        IPage<CustomerDailyStatReportResp> page = this.baseMapper.selectCustomerDailyStatReport(new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize()), query.getCustomerId(), start, end);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<CustomerDailyStatReportResp> listCustomerDailyStatReport(CustomerStatReportQuery query,
                                                                         SortQuery sortQuery) {
        LocalDateTime start = null;
        LocalDateTime end = null;
        if (ObjectUtils.isNotEmpty(query.getTransTime()) && query.getTransTime().length == 2) {
            start = query.getTransTime()[0];
            end = query.getTransTime()[1];
        }
        return this.baseMapper.listCustomerDailyStatReport(query.getCustomerId(), start, end);
    }

    @Override
    public CustomerDO getByTelegramChatId(String telegramChatId) {
        // 判断当前群是否只绑定一个客户
        long count = this.count(Wrappers.<CustomerDO>lambdaQuery().eq(CustomerDO::getTelegramChatId, telegramChatId));
        if (count != 1) {
            return null;
        }
        return this.getOne(Wrappers.<CustomerDO>lambdaQuery().eq(CustomerDO::getTelegramChatId, telegramChatId));
    }

    @Override
    public boolean checkRepeatTransfer(Long customerId, BigDecimal amount) {
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusHours(3);
        return customerBalanceRecordService.exists(Wrappers.<CustomerBalanceRecordDO>lambdaQuery()
                .eq(CustomerBalanceRecordDO::getCustomerId, customerId)
                .eq(CustomerBalanceRecordDO::getType, CustomerBalanceTypeEnum.SYSTEM)
                .eq(CustomerBalanceRecordDO::getAmount, amount)
                .between(CustomerBalanceRecordDO::getTransTime, start, end));
    }

    @Override
    public List<CustomerDailyExcel> dailyList(CustomerDO customer) {
        List<CustomerBalanceRecordDO> list = customerBalanceRecordService.list(new LambdaQueryWrapper<CustomerBalanceRecordDO>().eq(CustomerBalanceRecordDO::getCustomerId, customer.getId())
                .orderByAsc(CustomerBalanceRecordDO::getTransTime, CustomerBalanceRecordDO::getId));

        List<CustomerDailyExcel> excelList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            CustomerBalanceRecordDO record = list.get(i);
            if (record.getType() == CustomerBalanceTypeEnum.RECHARGE_FEE) {
                continue;
            }
            LocalDateTime transTime = record.getTransTime();
            CustomerDailyExcel customerDailyExcel = new CustomerDailyExcel();
            customerDailyExcel.setDate(transTime.toLocalDate().toString());
            customerDailyExcel.setAdAccountId(record.getPlatformAdId());
            customerDailyExcel.setCustomer(customer.getName());
            customerDailyExcel.setRemark(record.getRemark().replaceAll("【旧数据导入】", ""));

            if (record.getType() == CustomerBalanceTypeEnum.SYSTEM) {
                BigDecimal payment = record.getAmount();
                customerDailyExcel.setPayment(payment);
                BigDecimal fee = BigDecimal.ZERO;
                if (i + 1 < list.size()) {
                    if (list.get(i + 1).getType() == CustomerBalanceTypeEnum.RECHARGE_FEE) {
                        fee = list.get(i + 1).getAmount();
                    }
                }
                if (fee.compareTo(BigDecimal.ZERO) > 0) {
                    customerDailyExcel.setServiceFee(fee);
                }
                customerDailyExcel.setRechargeAdAccountAmount(payment.subtract(fee));
            }
            syncData(customerDailyExcel, record);
            excelList.add(customerDailyExcel);
        }

        return excelList;
    }

    @Override
    public CustomerInfoResp getInfo(Long id) {
        CustomerDO customerDO = getById(id);
        if (customerDO == null) {
            throw new BusinessException("没找到客户");
        }
        AdAccountOrderService adAccountOrderService = SpringUtil.getBean(AdAccountOrderService.class);
        List<String> list = adAccountOrderService.list(new LambdaQueryWrapper<AdAccountOrderDO>().eq(AdAccountOrderDO::getCustomerId, id)
                        .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED))
                .stream()
                .map(AdAccountOrderDO::getAdAccountId)
                .toList();
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        CustomerInfoResp customerInfoResp = new CustomerInfoResp();
        customerInfoResp.setTotalAdAccount(list.size());

        List<AdAccountBalanceRecordDO> adAccountBalanceRecordDOList = adAccountBalanceRecordService.list(new LambdaQueryWrapper<AdAccountBalanceRecordDO>().in(AdAccountBalanceRecordDO::getPlatformAdId, list)
                .eq(AdAccountBalanceRecordDO::getType, AdAccountBalanceTypeEnum.RECHARGE));
        // 充值金额
        BigDecimal totalRecharge = adAccountBalanceRecordDOList.stream()
                .map(AdAccountBalanceRecordDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal withdrawalAmount = customerBalanceRecordService.list(new LambdaQueryWrapper<CustomerBalanceRecordDO>().eq(CustomerBalanceRecordDO::getCustomerId, customerDO.getId())
                        .in(CustomerBalanceRecordDO::getType, CustomerBalanceTypeEnum.AD_ACCOUNT_CLEAR, CustomerBalanceTypeEnum.AD_ACCOUNT_REDUCE))
                .stream()
                .map(CustomerBalanceRecordDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setTotalRecharge(totalRecharge.subtract(withdrawalAmount));

        // 平均充值
        customerInfoResp.setAverageRecharge(customerInfoResp.getTotalRecharge()
                .divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP));

        // 停用广告户
        long count = adAccountService.count(new LambdaQueryWrapper<AdAccountDO>().in(AdAccountDO::getPlatformAdId, list)
                .eq(AdAccountDO::getAccountStatus, AdAccountStatusEnum.BANNED));
        customerInfoResp.setDeadAdAccountNum(Math.toIntExact(count));
        // 死户率
        BigDecimal deadProbabilities = BigDecimal.valueOf(customerInfoResp.getDeadAdAccountNum())
                .divide(BigDecimal.valueOf(customerInfoResp.getTotalAdAccount()), 2, RoundingMode.HALF_UP);
        customerInfoResp.setDeadProbabilities(deadProbabilities);
        // 当日充值
        BigDecimal toDayRecharge = adAccountBalanceRecordDOList.stream()
                .filter(s -> s.getTransTime().toLocalDate().equals(LocalDate.now()))
                .map(AdAccountBalanceRecordDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setToDayRecharge(toDayRecharge);

        AdAccountInsightService adAccountInsightService = SpringUtil.getBean(AdAccountInsightService.class);

        List<AdAccountInsightDO> accountInsight = adAccountInsightService.list(new LambdaQueryWrapper<AdAccountInsightDO>().in(AdAccountInsightDO::getAdAccountId, list));
        // 昨日花费
        BigDecimal yesterdaySpend = accountInsight.stream()
                .filter(s -> s.getStatDate().equals(LocalDate.now().minusDays(1)))
                .map(AdAccountInsightDO::getSpend)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setYesterdaySpend(yesterdaySpend);
        // 总花费
        BigDecimal totalSpend = accountInsight.stream()
                .map(AdAccountInsightDO::getSpend)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setTotalSpend(totalSpend);
        // 平均花费
        BigDecimal averageSpend = totalSpend.divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
        customerInfoResp.setAverageSpend(averageSpend);

        Set<String> cards = adAccountCardService.list(new LambdaQueryWrapper<AdAccountCardDO>().in(AdAccountCardDO::getPlatformAdId, list)
                        .ne(AdAccountCardDO::getFullCardNumber, ""))
                .stream()
                .map(AdAccountCardDO::getFullCardNumber)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        // 卡台余额
        BigDecimal cardBalance = cardService.list(new LambdaQueryWrapper<CardDO>().in(CardDO::getCardNumber, cards))
                .stream()
                .map(CardDO::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfoResp.setCardBalance(cardBalance);

        return customerInfoResp;
    }

    @Override
    public CustomerDO getByName(String customerName) {
        return this.getOne(Wrappers.<CustomerDO>lambdaQuery().eq(CustomerDO::getName, customerName));
    }

    @Override
    public String getName(Long id) {
        CustomerDO entity = this.getById(id);

        return null != entity ? entity.getName() : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStatus(List<Long> ids, Integer status, LocalDateTime terminateTime) {
        // 检查状态是否合法
        CheckUtils.throwIf(!CustomerStatusEnum.NORMAL.getValue()
                .equals(status) && !CustomerStatusEnum.TERMINATED.getValue().equals(status), "状态不合法");

        LambdaUpdateWrapper<CustomerDO> updateWrapper = Wrappers.<CustomerDO>lambdaUpdate()
                .set(CustomerDO::getStatus, status)
                .in(CustomerDO::getId, ids);

        // 根据状态设置或清空终止时间
        if (CustomerStatusEnum.NORMAL.getValue().equals(status)) {
            // 正常状态，清空终止时间
            updateWrapper.set(CustomerDO::getTerminateTime, null);
        } else if (CustomerStatusEnum.TERMINATED.getValue().equals(status)) {
            // 终止状态，设置当前时间为终止时间
            terminateTime = null == terminateTime ? LocalDateTime.now() : terminateTime;
            updateWrapper.set(CustomerDO::getTerminateTime, terminateTime);
        }

        // 批量更新状态和终止时间
        this.update(updateWrapper);
    }

    @Override
    public CustomerOpenResp open(Long id, CustomerOpenReq req) {
        CustomerDO customer = getById(id);
        CheckUtils.throwIfNull(customer, "客户不存在");
        CheckUtils.throwIf(StringUtils.isNotBlank(customer.getPassword()), "客户已经存在密码");
        boolean exists = exists(new LambdaQueryWrapper<CustomerDO>().eq(CustomerDO::getUsername, req.getUsername()));
        CheckUtils.throwIf(exists, "用户名已存在");
        // 生成强度较高的随机密码
        String password = RandomStringUtils.randomAlphanumeric(12);
        customer.setUsername(req.getUsername());
        customer.setPassword(DigestUtils.sha256Hex(password));
        this.updateById(customer);
        return CustomerOpenResp.builder().customerName(customer.getName()).username(req.getUsername()).password(password).build();
    }

    @Override
    public void resetPassword(Long id, CustomerOpenReq req) {
        CustomerDO customer = getById(id);
        CheckUtils.throwIfNull(customer, "客户不存在");
        CheckUtils.throwIfBlank(req.getPassword(), "请输入密码");

        customer.setPassword(DigestUtils.sha256Hex(req.getPassword()));
        this.updateById(customer);
    }

    @Override
    public void activation(CustomerActivationReq req) {
        lambdaUpdate()
                .eq(CustomerDO::getId, req.getId())
                .set(CustomerDO::getStatus, CustomerStatusEnum.NORMAL)
                .set(CustomerDO::getCooperateTime, req.getCooperateTime())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long rechargeFromRobot(Long customerId,
                                  BigDecimal amount,
                                  RechargeFeeHandleMethodEnum feeHandleMethodEnum,
                                  Long createUser,
                                  String remark) {
        // 计算充值手续费
        CustomerDO customer = this.getById(customerId);
        BigDecimal fee = BigDecimal.ZERO;
        if (feeHandleMethodEnum.equals(RechargeFeeHandleMethodEnum.REDUCE_FROM_RECHARGE_AMOUNT)) {
            fee = CommonUtils.divide100(amount.multiply(customer.getFeeRatePercent()), null);
        } else if (feeHandleMethodEnum.equals(RechargeFeeHandleMethodEnum.RECHARGE_INCLUDE_FEE)) {
            BigDecimal onePart = amount.divide(customer.getFeeRatePercent()
                    .add(BigDecimal.valueOf(100)), 4, RoundingMode.HALF_UP);
            fee = onePart.multiply(customer.getFeeRatePercent()).setScale(2, RoundingMode.HALF_UP);
        }
        // 发送额度汇总信息
        BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(customer.getId());
        Long id = this.changeAmount(customerId, null, amount, CustomerBalanceTypeEnum.SYSTEM, null, remark, null, createUser);
        this.changeAmount(customerId, null, fee.negate(), CustomerBalanceTypeEnum.RECHARGE_FEE, null, remark, null, createUser);
        CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, amount, customer.getBalance(), amount.subtract(fee));
        SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
        return id;
    }

    @Override
    public PageResp<CustomerOrderStatisticsResp> selectCustomerOrderStatisticsPage(CustomerOrderStatisticsQuery query,
                                                                                   PageQuery pageQuery) {
        IPage<CustomerOrderStatisticsResp> page = baseMapper.selectCustomerOrderStatisticsPage(new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize()), query);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    private void syncData(CustomerDailyExcel excel, CustomerBalanceRecordDO record) {
        if (record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_RECHARGE) {
            excel.setRechargeAmount(record.getAmount());
        } else if (record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_CLEAR || record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_REDUCE) {
            excel.setWithdrawalAmount(record.getAmount());
        } else if (record.getType() == CustomerBalanceTypeEnum.DEDUCE) {
            excel.setRefund(record.getAmount());
        } else if (record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_BUY) {
            excel.setOpeningFee(record.getAmount());
            excel.setRechargeAdAccountAmount(record.getAmount().negate());
        } else if (record.getType() == CustomerBalanceTypeEnum.MIGRATE) {
            if (record.getAction().equals(TransactionActionEnum.IN)) {
                excel.setAdjustment(record.getAmount());
            } else {
                excel.setAdjustment(record.getAmount().negate());
            }
        } else if (record.getType() == CustomerBalanceTypeEnum.AD_ACCOUNT_REFUND) {
            excel.setOpeningFee(record.getAmount().negate());
            excel.setRechargeAdAccountAmount(record.getAmount());
        }
    }
}