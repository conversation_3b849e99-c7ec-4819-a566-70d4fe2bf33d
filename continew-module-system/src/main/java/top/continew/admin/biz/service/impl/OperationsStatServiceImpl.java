package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.model.query.OperationsStatQuery;
import top.continew.admin.biz.model.resp.OperationsStatResp;
import top.continew.admin.biz.service.OperationsStatService;
import top.continew.admin.system.mapper.UserMapper;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;

@Service
@RequiredArgsConstructor
public class OperationsStatServiceImpl implements OperationsStatService {

    private final UserMapper userMapper;

    @Override
    public PageResp<OperationsStatResp> page(OperationsStatQuery query, PageQuery pageQuery) {

        IPage<OperationsStatResp> page = userMapper.selectOperationsStat(new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize()),query.getDate());
        return new PageResp<>(page.getRecords(), page.getTotal());
    }
}
