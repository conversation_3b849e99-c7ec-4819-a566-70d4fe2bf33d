package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.BusinessManagerTypeEnum;
import top.continew.admin.biz.enums.MaterialTypeEnum;
import top.continew.admin.biz.enums.PurchaseOrderStatusEnum;
import top.continew.admin.biz.enums.PurchaseOrderTypeEnum;
import top.continew.admin.biz.mapper.PurchaseOrderMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.query.PurchaseOrderQuery;
import top.continew.admin.biz.model.query.PurchaseReceiveOrderQuery;
import top.continew.admin.biz.model.req.IdsReq;
import top.continew.admin.biz.model.req.PurchaseOrderPayReq;
import top.continew.admin.biz.model.req.PurchaseOrderReceiveReq;
import top.continew.admin.biz.model.req.PurchaseOrderReq;
import top.continew.admin.biz.model.resp.PurchaseOrderCheckResp;
import top.continew.admin.biz.model.resp.PurchaseOrderDetailResp;
import top.continew.admin.biz.model.resp.PurchaseOrderResp;
import top.continew.admin.biz.model.resp.PurchaseReceiveOrderResp;
import top.continew.admin.biz.service.*;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * m 采购订单业务实现
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Service
@RequiredArgsConstructor
public class PurchaseOrderServiceImpl extends BaseServiceImpl<PurchaseOrderMapper, PurchaseOrderDO, PurchaseOrderResp, PurchaseOrderDetailResp, PurchaseOrderQuery, PurchaseOrderReq> implements PurchaseOrderService {

    private final MaterialService materialService;

    private final PurchaseReceiveOrderService purchaseReceiveOrderService;

    private final FbAccountService fbAccountService;

    private final PersonalAccountService personalAccountService;

    private final BusinessManagerService businessManagerService;

    private final BusinessManagerItemService businessManagerItemService;

    @Override
    public PageResp<PurchaseOrderResp> page(PurchaseOrderQuery query, PageQuery pageQuery) {
        QueryWrapper<PurchaseOrderDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, pageQuery);
        IPage<PurchaseOrderResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        page.getRecords().forEach(this::fill);
        for (PurchaseOrderResp item : page.getRecords()) {
            if (item.getReceiveDate() == null) {
                item.setWriteNum(0);
                item.setWritePrice(BigDecimal.ZERO);
                continue;
            }
            LocalDateTime start = LocalDateTimeUtil.beginOfDay(item.getReceiveDate());
            LocalDateTime end = LocalDateTimeUtil.endOfDay(item.getReceiveDate());
            int count;
            BigDecimal totalPrice;
            if (item.getType().equals(PurchaseOrderTypeEnum.BIG_BLACK_ACCOUNT)) {
                List<FbAccountDO> fbAccountList = fbAccountService.list(Wrappers.<FbAccountDO>lambdaQuery()
                    .eq(FbAccountDO::getChannelId, item.getChannelId())
                    .between(FbAccountDO::getCreateTime, start, end));
                count = fbAccountList.size();
                totalPrice = fbAccountList.stream().map(FbAccountDO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else if (item.getType().equals(PurchaseOrderTypeEnum.THREE_APPEAR_ACCOUNT)) {
                List<PersonalAccountDO> personalAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
                    .eq(PersonalAccountDO::getChannelId, item.getChannelId())
                    .eq(PersonalAccountDO::getIsAfterSale, false)
                    .between(PersonalAccountDO::getCreateTime, start, end));
                count = personalAccountList.size();
                totalPrice = personalAccountList.stream()
                    .map(PersonalAccountDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else if (item.getType().equals(PurchaseOrderTypeEnum.BM1) || item.getType()
                .equals(PurchaseOrderTypeEnum.BM1_ENTERPRISE_AUTH) || item.getType()
                .equals(PurchaseOrderTypeEnum.BM3) || item.getType().equals(PurchaseOrderTypeEnum.BM5) || item.getType()
                .equals(PurchaseOrderTypeEnum.BM10)) {
                List<BusinessManagerDO> bmList = businessManagerService.list(Wrappers.<BusinessManagerDO>lambdaQuery()
                    .eq(BusinessManagerDO::getChannelId, item.getChannelId())
                    .eq(BusinessManagerDO::getIsBu, false)
                    .eq(BusinessManagerDO::getType, BusinessManagerTypeEnum.getByPurchaseOrderTYpe(item.getType()))
                    .eq(item.getType()
                        .equals(PurchaseOrderTypeEnum.BM1_ENTERPRISE_AUTH), BusinessManagerDO::getIsEnterpriseAuth, true)
                    .between(BusinessManagerDO::getCreateTime, start, end));
                count = bmList.size();
                totalPrice = bmList.stream()
                    .map(BusinessManagerDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                List<BusinessManagerItemDO> bmItemList = businessManagerItemService.list(Wrappers.<BusinessManagerItemDO>lambdaQuery()
                    .eq(BusinessManagerItemDO::getChannelId, item.getChannelId())
                    .eq(BusinessManagerItemDO::getIsBu, false)
                    .eq(BusinessManagerItemDO::getType, BusinessManagerTypeEnum.getByPurchaseOrderTYpe(item.getType()))
                    .between(BusinessManagerItemDO::getCreateTime, start, end));
                count = bmItemList.size();
                totalPrice = bmItemList.stream()
                    .map(BusinessManagerItemDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            item.setWriteNum(count);
            item.setWritePrice(totalPrice);
        }
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(PurchaseOrderQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<PurchaseOrderDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<PurchaseOrderResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        for (PurchaseOrderResp item : entityList) {
            if (item.getReceiveDate() == null) {
                item.setWriteNum(0);
                item.setWritePrice(BigDecimal.ZERO);
                continue;
            }
            LocalDateTime start = LocalDateTimeUtil.beginOfDay(item.getReceiveDate());
            LocalDateTime end = LocalDateTimeUtil.endOfDay(item.getReceiveDate());
            int count;
            BigDecimal totalPrice;
            if (item.getType().equals(PurchaseOrderTypeEnum.BIG_BLACK_ACCOUNT)) {
                List<FbAccountDO> fbAccountList = fbAccountService.list(Wrappers.<FbAccountDO>lambdaQuery()
                    .eq(FbAccountDO::getChannelId, item.getChannelId())
                    .between(FbAccountDO::getCreateTime, start, end));
                count = fbAccountList.size();
                totalPrice = fbAccountList.stream().map(FbAccountDO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else if (item.getType().equals(PurchaseOrderTypeEnum.THREE_APPEAR_ACCOUNT)) {
                List<PersonalAccountDO> personalAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
                    .eq(PersonalAccountDO::getChannelId, item.getChannelId())
                    .eq(PersonalAccountDO::getIsAfterSale, false)
                    .between(PersonalAccountDO::getCreateTime, start, end));
                count = personalAccountList.size();
                totalPrice = personalAccountList.stream()
                    .map(PersonalAccountDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else if (item.getType().equals(PurchaseOrderTypeEnum.BM1) || item.getType()
                .equals(PurchaseOrderTypeEnum.BM1_ENTERPRISE_AUTH) || item.getType()
                .equals(PurchaseOrderTypeEnum.BM3) || item.getType().equals(PurchaseOrderTypeEnum.BM5) || item.getType()
                .equals(PurchaseOrderTypeEnum.BM10)) {
                List<BusinessManagerDO> bmList = businessManagerService.list(Wrappers.<BusinessManagerDO>lambdaQuery()
                    .eq(BusinessManagerDO::getChannelId, item.getChannelId())
                    .eq(BusinessManagerDO::getIsBu, false)
                    .eq(BusinessManagerDO::getType, BusinessManagerTypeEnum.getByPurchaseOrderTYpe(item.getType()))
                    .eq(item.getType()
                        .equals(PurchaseOrderTypeEnum.BM1_ENTERPRISE_AUTH), BusinessManagerDO::getIsEnterpriseAuth, true)
                    .between(BusinessManagerDO::getCreateTime, start, end));
                count = bmList.size();
                totalPrice = bmList.stream()
                    .map(BusinessManagerDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                List<BusinessManagerItemDO> bmItemList = businessManagerItemService.list(Wrappers.<BusinessManagerItemDO>lambdaQuery()
                    .eq(BusinessManagerItemDO::getChannelId, item.getChannelId())
                    .eq(BusinessManagerItemDO::getIsBu, false)
                    .eq(BusinessManagerItemDO::getType, BusinessManagerTypeEnum.getByPurchaseOrderTYpe(item.getType()))
                    .between(BusinessManagerItemDO::getCreateTime, start, end));
                count = bmItemList.size();
                totalPrice = bmItemList.stream()
                    .map(BusinessManagerItemDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            item.setWriteNum(count);
            item.setWritePrice(totalPrice);
        }
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    protected void beforeAdd(PurchaseOrderReq req) {

        if (req.getPurchaseTime() == null) {
            req.setPurchaseTime(LocalDate.now());
        }

        if (ObjectUtil.isAllNotEmpty(req.getReceiveNum(), req.getReceivePrice())) {
            req.setReceiveUser(UserContextHolder.getNickname());
            req.setStatus(PurchaseOrderStatusEnum.RECEIVE);
        } else {
            if (req.getReceiveNum() != null && req.getReceivePrice() == null) {
                throw new BusinessException("验收金额不能为空");
            }
            if (req.getReceiveNum() == null && req.getReceivePrice() != null) {
                throw new BusinessException("验收数量不能为空");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchPay(PurchaseOrderPayReq reqs) {
        LocalDateTime payTime = Optional.ofNullable(reqs.getPayTime()).orElse(LocalDateTime.now());
        for (PurchaseOrderPayReq.PurchaseOrderPayItemReq req : reqs.getItems()) {
            PurchaseOrderDO order = this.getById(req.getId());
            if (order.getIsPay()) {
                continue;
            }
            PurchaseOrderDO update = new PurchaseOrderDO();
            update.setId(order.getId());
            update.setIsPay(true);
            update.setPayPrice(req.getPayPrice());
            update.setPayTime(payTime);
            if (reqs.getIsFinish()) {
                update.setStatus(PurchaseOrderStatusEnum.FINISH);
                update.setFinishTime(LocalDateTime.now());
            }
            this.updateById(update);
            if (req.getPayPrice().compareTo(BigDecimal.ZERO) > 0) {
                // 同步新增物料成本录入
                List<PurchaseReceiveOrderDO> items = purchaseReceiveOrderService.list(Wrappers.<PurchaseReceiveOrderDO>lambdaQuery()
                    .eq(PurchaseReceiveOrderDO::getPurchaseOrderId, req.getId()));
                int receiveNum = 0;
                for (PurchaseReceiveOrderDO item : items) {
                    receiveNum += item.getReceiveNum();
                }
                if (receiveNum == 0) {
                    receiveNum = order.getExpectNum();
                }
                MaterialDO material = new MaterialDO();
                material.setPayDate(LocalDateTime.now());
                material.setChannelId(order.getChannelId());
                material.setType(MaterialTypeEnum.getByPurchaseOrderType(order.getType()));
                material.setNum(receiveNum);
                material.setPayPrice(req.getPayPrice());
                material.setPayDate(payTime);
                material.setRemark(order.getRemark());
                materialService.save(material);
            }
        }
    }

    @Override
    public void batchFinish(IdsReq idsReq) {
        for (Long id : idsReq.getIds()) {
            PurchaseOrderDO order = this.getById(id);
            if (!order.getIsPay()) {
                continue;
            }
            if (!order.getStatus().equals(PurchaseOrderStatusEnum.RECEIVE)) {
                continue;
            }
            PurchaseOrderDO update = new PurchaseOrderDO();
            update.setId(order.getId());
            update.setStatus(PurchaseOrderStatusEnum.FINISH);
            update.setFinishTime(LocalDateTime.now());
            this.updateById(update);
        }
    }

    @Override
    public List<PurchaseOrderCheckResp> check(LocalDateTime[] createTime) {
        LocalDateTime start = createTime[0];
        LocalDateTime end = createTime[1];
        PurchaseReceiveOrderQuery query = new PurchaseReceiveOrderQuery();
        query.setReceiveTime(createTime);
        List<PurchaseReceiveOrderResp> list = purchaseReceiveOrderService.list(query, new SortQuery());
        List<PurchaseOrderCheckResp> result = new ArrayList<>();
        for (PurchaseReceiveOrderResp purchaseReceiveOrderResp : list) {
            PurchaseOrderCheckResp exist = result.stream()
                .filter(v -> v.getChannelId().equals(purchaseReceiveOrderResp.getChannelId()) && v.getType()
                    .equals(purchaseReceiveOrderResp.getType()))
                .findFirst()
                .orElse(null);
            if (exist == null) {
                PurchaseOrderCheckResp item = new PurchaseOrderCheckResp();
                item.setChannelId(purchaseReceiveOrderResp.getChannelId());
                item.setType(purchaseReceiveOrderResp.getType());
                item.setChannelName(purchaseReceiveOrderResp.getChannelName());
                item.setPurchaseNum(purchaseReceiveOrderResp.getPurchaseNum());
                item.setPurchasePrice(purchaseReceiveOrderResp.getPurchasePrice());
                item.setReceivePrice(purchaseReceiveOrderResp.getReceivePrice());
                item.setReceiveNum(purchaseReceiveOrderResp.getReceiveNum());
                result.add(item);
            } else {
                exist.setReceiveNum(exist.getReceiveNum() + purchaseReceiveOrderResp.getReceiveNum());
                exist.setReceivePrice(exist.getReceivePrice().add(purchaseReceiveOrderResp.getReceivePrice()));
            }
        }
        for (PurchaseOrderCheckResp purchaseReceiveOrderResp : result) {
            int count = 0;
            BigDecimal totalPrice = BigDecimal.ZERO;
            if (purchaseReceiveOrderResp.getType().equals(PurchaseOrderTypeEnum.BIG_BLACK_ACCOUNT)) {
                List<FbAccountDO> fbAccountList = fbAccountService.list(Wrappers.<FbAccountDO>lambdaQuery()
                    .eq(FbAccountDO::getChannelId, purchaseReceiveOrderResp.getChannelId())
                    .between(FbAccountDO::getCreateTime, start, end));
                count = fbAccountList.size();
                totalPrice = fbAccountList.stream().map(FbAccountDO::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else if (purchaseReceiveOrderResp.getType().equals(PurchaseOrderTypeEnum.THREE_APPEAR_ACCOUNT)) {
                List<PersonalAccountDO> personalAccountList = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
                    .eq(PersonalAccountDO::getChannelId, purchaseReceiveOrderResp.getChannelId())
                    .eq(PersonalAccountDO::getIsAfterSale, false)
                    .between(PersonalAccountDO::getCreateTime, start, end));
                count = personalAccountList.size();
                totalPrice = personalAccountList.stream()
                    .map(PersonalAccountDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else if (purchaseReceiveOrderResp.getType().equals(PurchaseOrderTypeEnum.HISTORY_BILLING_ACCOUNT)) {

            } else if (purchaseReceiveOrderResp.getType()
                .equals(PurchaseOrderTypeEnum.BM1) || purchaseReceiveOrderResp.getType()
                .equals(PurchaseOrderTypeEnum.BM3) || purchaseReceiveOrderResp.getType()
                .equals(PurchaseOrderTypeEnum.BM5) || purchaseReceiveOrderResp.getType()
                .equals(PurchaseOrderTypeEnum.BM10) || purchaseReceiveOrderResp.getType()
                .equals(PurchaseOrderTypeEnum.BM50)) {
                List<BusinessManagerDO> bmList = businessManagerService.list(Wrappers.<BusinessManagerDO>lambdaQuery()
                    .eq(BusinessManagerDO::getChannelId, purchaseReceiveOrderResp.getChannelId())
                    .eq(BusinessManagerDO::getIsBu, false)
                    .eq(BusinessManagerDO::getType, BusinessManagerTypeEnum.getByPurchaseOrderTYpe(purchaseReceiveOrderResp.getType()))
                    .between(BusinessManagerDO::getCreateTime, start, end));
                count = bmList.size();
                totalPrice = bmList.stream()
                    .map(BusinessManagerDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                List<BusinessManagerItemDO> bmItemList = businessManagerItemService.list(Wrappers.<BusinessManagerItemDO>lambdaQuery()
                    .eq(BusinessManagerItemDO::getChannelId, purchaseReceiveOrderResp.getChannelId())
                    .eq(BusinessManagerItemDO::getIsBu, false)
                    .eq(BusinessManagerItemDO::getType, BusinessManagerTypeEnum.getByPurchaseOrderTYpe(purchaseReceiveOrderResp.getType()))
                    .between(BusinessManagerItemDO::getCreateTime, start, end));
                count = bmItemList.size();
                totalPrice = bmItemList.stream()
                    .map(BusinessManagerItemDO::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            purchaseReceiveOrderResp.setWriteNum(count);
            purchaseReceiveOrderResp.setWritePrice(totalPrice);
            if (count != purchaseReceiveOrderResp.getReceiveNum() || totalPrice.compareTo(purchaseReceiveOrderResp.getReceivePrice()) != 0) {
                purchaseReceiveOrderResp.setMessage("数据核对有误，请排查");
            } else {
                purchaseReceiveOrderResp.setMessage("数据核对成功");
            }
        }
        return result;
    }

    @Override
    public void receive(PurchaseOrderReceiveReq req) {
        PurchaseOrderDO order = this.getById(req.getId());
        CheckUtils.throwIf(req.getReceiveNum() > order.getExpectNum(), "验收数量不能大于采购数量");
        PurchaseOrderDO update = new PurchaseOrderDO();
        update.setId(order.getId());
        update.setReceiveNum(req.getReceiveNum());
        update.setReceivePrice(req.getReceivePrice());
        update.setReceiveUser(UserContextHolder.getNickname());
        if (order.getStatus().equals(PurchaseOrderStatusEnum.PROCESS)) {
            update.setStatus(PurchaseOrderStatusEnum.RECEIVE);
        }
        this.updateById(update);
    }
}