package top.continew.admin.biz.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.SalesDailySummaryMapper;
import top.continew.admin.biz.model.entity.SalesDailySummaryDO;
import top.continew.admin.biz.model.query.SalesDailySummaryQuery;
import top.continew.admin.biz.model.req.SalesDailySummaryReq;
import top.continew.admin.biz.model.resp.SalesDailySummaryDetailResp;
import top.continew.admin.biz.model.resp.SalesDailySummaryResp;
import top.continew.admin.biz.service.SalesDailySummaryService;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

/**
 * 商务日报业务实现
 *
 * <AUTHOR>
 * @since 2025/08/01 14:29
 */
@Service
@RequiredArgsConstructor
public class SalesDailySummaryServiceImpl extends BaseServiceImpl<SalesDailySummaryMapper, SalesDailySummaryDO, SalesDailySummaryResp, SalesDailySummaryDetailResp, SalesDailySummaryQuery, SalesDailySummaryReq> implements SalesDailySummaryService {

    @Override
    public void telegramSave(SalesDailySummaryDO salesDailySummaryDO) {

        SalesDailySummaryDO salesDailySummary = lambdaQuery().eq(SalesDailySummaryDO::getCreateUser, salesDailySummaryDO.getCreateUser())
                .eq(SalesDailySummaryDO::getRecordDate, salesDailySummaryDO.getRecordDate())
                .one();
        if (salesDailySummary != null) {
            // 存在旧记录，设置ID进行更新
            salesDailySummaryDO.setId(salesDailySummary.getId());
        }
        saveOrUpdate(salesDailySummaryDO);

    }
}