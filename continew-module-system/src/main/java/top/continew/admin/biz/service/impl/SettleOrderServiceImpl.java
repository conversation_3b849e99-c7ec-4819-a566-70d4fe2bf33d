package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.CustomerSettleTypeEnum;
import top.continew.admin.biz.mapper.SettleOrderMapper;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.SettleOrderDO;
import top.continew.admin.biz.model.entity.SettleOrderItemDO;
import top.continew.admin.biz.model.query.SettleOrderQuery;
import top.continew.admin.biz.model.req.SettleOrderReq;
import top.continew.admin.biz.model.resp.AdAccountOrderResp;
import top.continew.admin.biz.model.resp.SettleOrderDetailResp;
import top.continew.admin.biz.model.resp.SettleOrderResp;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.admin.biz.service.SettleOrderItemService;
import top.continew.admin.biz.service.SettleOrderService;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 结算订单业务实现
 *
 * <AUTHOR>
 * @since 2025/03/20 14:56
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SettleOrderServiceImpl extends BaseServiceImpl<SettleOrderMapper, SettleOrderDO, SettleOrderResp, SettleOrderDetailResp, SettleOrderQuery, SettleOrderReq> implements SettleOrderService {

    private final CustomerService customerService;

    private final AdAccountOrderService adAccountOrderService;

    private final SettleOrderItemService settleOrderItemService;

    @Override
    public Long add(SettleOrderReq req) {
        CustomerDO customer = customerService.getById(req.getCustomerId());
        CheckUtils.throwIf(customer.getSettleType().equals(CustomerSettleTypeEnum.ONE), "该客户未开启实消模式");
        log.info("【预付实消】{}进行实消结算...", customer.getName());
        List<AdAccountOrderResp> orderList = adAccountOrderService.selectCustomerUnClearOrderList(req.getCustomerId());
        CheckUtils.throwIf(orderList.isEmpty(), "该客户未有授权完成的下户订单");
        BigDecimal totalSpent = BigDecimal.ZERO;
        List<SettleOrderItemDO> items = new ArrayList<>();
        for (AdAccountOrderResp order : orderList) {
            log.info("【预付实消】{}-{}当前消耗{}", customer.getName(), order.getAdAccountId(), order.getAmountSpent());
            if (order.getAmountSpent() != null) {
                totalSpent = totalSpent.add(order.getAmountSpent());
            }
            SettleOrderItemDO item = new SettleOrderItemDO();
            item.setPlatformAdId(order.getAdAccountId());
            item.setSpent(order.getAmountSpent());
            items.add(item);
        }
        log.info("【预付实消】{}上一次结算消耗{}，当前总消耗{}", customer.getName(), customer.getLastSettleSpent(), totalSpent);
        SettleOrderDO settleOrder = new SettleOrderDO();
        settleOrder.setCustomerId(req.getCustomerId());
        settleOrder.setTotalSpent(totalSpent);
        settleOrder.setSettleSpent(totalSpent.subtract(customer.getLastSettleSpent()));
        settleOrder.setSettleTime(LocalDateTime.now());
        this.save(settleOrder);
        for (SettleOrderItemDO item : items) {
            item.setOrderId(settleOrder.getId());
        }
        settleOrderItemService.saveBatch(items);
        customerService.update(Wrappers.<CustomerDO>lambdaUpdate()
            .set(CustomerDO::getLastSettleSpent, totalSpent)
            .eq(CustomerDO::getId, req.getCustomerId()));
        log.info("【预付实消】====={}结算完成=====", customer.getName());
        return settleOrder.getId();
    }
}