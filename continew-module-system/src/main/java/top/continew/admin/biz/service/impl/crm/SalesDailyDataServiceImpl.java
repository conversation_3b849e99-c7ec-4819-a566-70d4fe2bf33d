package top.continew.admin.biz.service.impl.crm;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;

import net.dreamlu.mica.core.utils.BeanUtil;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.AccountTypeEnum;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.admin.biz.enums.LeadStatusEnum;
import top.continew.admin.biz.enums.OpportunityStatusEnum;
import top.continew.admin.biz.model.entity.crm.CustomerAccountRelDO;
import top.continew.admin.biz.model.entity.crm.LeadDO;
import top.continew.admin.biz.model.req.CustomerReq;
import top.continew.admin.biz.model.req.crm.*;
import top.continew.admin.biz.model.resp.crm.*;
import top.continew.admin.biz.service.crm.*;
import top.continew.admin.common.constant.SysConstants;
import top.continew.admin.system.model.resp.MenuResp;
import top.continew.admin.system.model.resp.RoleDetailResp;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.admin.biz.mapper.crm.SalesDailyDataMapper;
import top.continew.admin.biz.model.entity.crm.SalesDailyDataDO;
import top.continew.admin.biz.model.query.crm.SalesDailyDataQuery;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 商务每日数据业务实现
 *
 * <AUTHOR>
 * @since 2025/07/11 10:55
 */
@Service
@RequiredArgsConstructor
public class SalesDailyDataServiceImpl extends BaseServiceImpl<SalesDailyDataMapper, SalesDailyDataDO, SalesDailyDataResp, SalesDailyDataDetailResp, SalesDailyDataQuery, SalesDailyDataReq> implements SalesDailyDataService {
    private final LeadService leadService;
    private final OpportunityService opportunityService;
    private final SocialAccountService socialAccountService;


    @Override
    public PageResp<SalesDailyDataResp> page(SalesDailyDataQuery query, PageQuery pageQuery) {
        // 创建分页对象
        Page<SalesDailyDataDO> page = new Page<>((long) pageQuery.getPage(), (long) pageQuery.getSize());
        // 调用自定义的分页查询方法
        IPage<SalesDailyDataResp> dataPage = baseMapper.selectDailyDataPage(page, query);

        // 转换为响应对象
        PageResp<SalesDailyDataResp> pageResp = PageResp.build(dataPage);
        pageResp.getList().forEach(this::fill);
        return pageResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<SalesDailyDataReq> addList) {
        if (CollUtil.isEmpty(addList)) {
            return;
        }

        Long currentUserId = StpUtil.getLoginIdAsLong();
        List<SalesDailyDataDO> dailyDataList = new ArrayList<>();
        List<String> duplicateMessages = new ArrayList<>();

        // 1. 检查addList内部是否有重复数据（通过账号类型、客户账号来判断）
        checkInternalDuplicates(addList, duplicateMessages);

        // 2. 转换数据并检查数据库中的重复数据
        for (SalesDailyDataReq item : addList) {
            SalesDailyDataDO data = new SalesDailyDataDO();
            BeanUtil.copyProperties(item, data);

            if (data.getRecordDate() == null) {
                data.setRecordDate(LocalDateTime.now().toLocalDate());
            }

            // 检查数据库中是否存在重复数据（通过账号类型、客户账号、创建人、记录日期来判断）
            checkDatabaseDuplicates(data, currentUserId, duplicateMessages);

            dailyDataList.add(data);
        }

        // 如果有重复数据，抛出异常
        if (!duplicateMessages.isEmpty()) {
            String errorMessage = "发现重复数据：" + String.join("；", duplicateMessages);
            CheckUtils.throwIf(true, errorMessage);
        }

        // 批量新增
        if (!dailyDataList.isEmpty()) {
            saveBatch(dailyDataList);
        }
    }

    /**
     * 检查addList内部是否有重复数据
     * 根据账号类型、客户账号来判断
     *
     * @param addList 待添加的数据列表
     * @param duplicateMessages 重复信息收集列表
     */
    private void checkInternalDuplicates(List<SalesDailyDataReq> addList, List<String> duplicateMessages) {
        for (int i = 0; i < addList.size(); i++) {
            SalesDailyDataReq current = addList.get(i);
            for (int j = i + 1; j < addList.size(); j++) {
                SalesDailyDataReq compare = addList.get(j);
                
                // 比较账号类型和客户账号
                if (current.getAccountType().equals(compare.getAccountType()) &&
                    StrUtil.equals(current.getCustomerAccount(), compare.getCustomerAccount())) {
                    
                    String message = String.format("客户账号[%s]", current.getCustomerAccount());
                    duplicateMessages.add(message);
                    break; // 避免重复添加相同的错误信息
                }
            }
        }
    }

    /**
     * 检查数据库中是否存在重复数据
     * 根据账号类型、客户账号、创建人、记录日期来判断
     *
     * @param data 待检查的数据
     * @param createUser 创建人ID
     * @param duplicateMessages 重复信息收集列表
     */
    private void checkDatabaseDuplicates(SalesDailyDataDO data, Long createUser, List<String> duplicateMessages) {
        List<SalesDailyDataDO> existingData = baseMapper.selectDuplicateData(
            data.getAccountType(),
            data.getCustomerAccount(),
            createUser
        );

        if (CollUtil.isNotEmpty(existingData)) {
            String message = String.format("客户账号[%s]", data.getCustomerAccount());
            duplicateMessages.add(message);
        }
    }


    @Override
    public void fill(Object obj) {
        super.fill(obj);
        if (obj instanceof SalesDailyDataResp resp) {
            Long socialAccountId = resp.getSocialAccountId();
            if(null != socialAccountId && socialAccountId > 0L){
                SocialAccountDetailResp accountResp = socialAccountService.get(resp.getSocialAccountId());
                resp.setSocialAccountName(null != accountResp? accountResp.getAccount() : StrUtil.EMPTY);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLead(Long id) {
        SalesDailyDataDO dailyData = baseMapper.selectById(id);
        CheckUtils.throwIfNull(dailyData, "数据不存在");

        LeadReq leadReq = new LeadReq();
        leadReq.setHandlerUserId(dailyData.getCreateUser());
        leadReq.setCustomerName(StrUtil.isBlank(dailyData.getCustomerName()) ? dailyData.getCustomerAccount() : dailyData.getCustomerName());
        //客户来源
        //判断账号类型，如果来源是微信，则为微信社群。如果来源为TG，则为TG社群
        if(AccountTypeEnum.WECHAT.getValue().equals(dailyData.getAccountType())){
            leadReq.setSourceId(721371495221166180L);
        }

        if(AccountTypeEnum.TG.getValue().equals(dailyData.getAccountType())){
            leadReq.setSourceId(721371616793067628L);
        }

        leadReq.setStatus(LeadStatusEnum.FOLLOWING_UP.getValue());
        leadReq.setRequirement(dailyData.getCustomerOverview());

        //客户联系方式
        CustomerAccountAddReq accountReq = new CustomerAccountAddReq();
        accountReq.setAccountType(dailyData.getAccountType());
        accountReq.setAccount(dailyData.getCustomerAccount());
        accountReq.setSocialAccountId(dailyData.getSocialAccountId());
        List<CustomerAccountAddReq> accounts = List.of(accountReq);
        leadReq.setAccounts(accounts);
        Long leadId = leadService.add(leadReq);

        //增加跟进
        LeadFollowReq followReq = new LeadFollowReq();
        followReq.setLeadId(leadId);
        followReq.setBusinessUserId(dailyData.getCreateUser());
        followReq.setContent(dailyData.getFirstContactNotes());
        followReq.setFollowTime(dailyData.getRecordDate().atTime(0, 0, 0));
        leadService.addFollow(followReq);

        SalesDailyDataDO updateData = new SalesDailyDataDO();
        updateData.setId(id);
        updateData.setLeadId(leadId);
        baseMapper.updateById(updateData);
        return leadId;

    }

    @Override
    protected void beforeUpdate(SalesDailyDataReq req, Long id) {
        SalesDailyDataDO dailyData = baseMapper.selectById(id);
        CheckUtils.throwIfNull(dailyData, "数据不存在");

        CheckUtils.throwIfNull(dailyData.getLeadId(), "已经创建线索，请删除后重新创建");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected void afterDelete(List<Long> ids) {
        List<Long> needDeleteIds = new ArrayList<>();
        for (Long id : ids) {
            SalesDailyDataDO dailyData = baseMapper.selectById(id);
            if (dailyData != null && dailyData.getLeadId() != null) {
                LeadDetailResp lead = leadService.get(dailyData.getLeadId());
                if(null != lead) {
                    CheckUtils.throwIf(LeadStatusEnum.CREATE_OPPORTUNITY.equals(lead.getStatus()), "关联线索已创建商机，不能删除");
                    needDeleteIds.add(lead.getId());
                }
            }
        }

        if(CollUtil.isNotEmpty(needDeleteIds)) {
            leadService.delete(needDeleteIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void convert(SalesDailyDataConvertReq req) {
        SalesDailyDataDO dailyData = baseMapper.selectById(req.getRecordId());
        CheckUtils.throwIfNull(dailyData, "数据不存在");
        // 转化线索 - 调用现有方法
        Long leadId = createLead(req.getRecordId());
        if ("opportunity".equals(req.getConvertType())) {
            LeadFollowReq followReq = new LeadFollowReq();
            followReq.setLeadId(leadId);
            followReq.setStatus(LeadStatusEnum.CREATE_OPPORTUNITY);
            followReq.setBusinessUserId(req.getBusinessUserId());
            followReq.setTelegramChatId(req.getTelegramChatId());
            followReq.setFollowTime(LocalDateTime.now());
            leadService.addFollow(followReq);

            if(null != req.getIsWon() && req.getIsWon()) {
                LeadDetailResp lead = leadService.get(leadId);
                if(null != lead && null != lead.getOpportunityId()) {
                    OpportunityFollowReq opportunityFollowReq = new OpportunityFollowReq();
                    opportunityFollowReq.setOpportunityId(lead.getOpportunityId());
                    opportunityFollowReq.setStatus(OpportunityStatusEnum.WON);
                    opportunityFollowReq.setFollowTime(LocalDateTime.now());
                    opportunityService.addFollow(opportunityFollowReq);
                }
            }
        }

    }

}