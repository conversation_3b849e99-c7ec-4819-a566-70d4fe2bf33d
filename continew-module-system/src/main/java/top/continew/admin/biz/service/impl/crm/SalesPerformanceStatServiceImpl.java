package top.continew.admin.biz.service.impl.crm;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.mapper.crm.SalesPerformanceStatMapper;
import top.continew.admin.biz.model.query.crm.SalesPerformanceStatQuery;
import top.continew.admin.biz.model.resp.crm.SalesPerformanceStatResp;
import top.continew.admin.biz.service.crm.SalesPerformanceStatService;

import java.util.List;

/**
 * 商务业绩统计 Service 实现
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class SalesPerformanceStatServiceImpl implements SalesPerformanceStatService {

    private final SalesPerformanceStatMapper salesPerformanceStatMapper;

    @Override
    public List<SalesPerformanceStatResp> listSalesPerformanceStat(SalesPerformanceStatQuery query) {
        return salesPerformanceStatMapper.selectSalesPerformanceStat(
            query.getSalesUserIds(),
            query.getStartDate(),
            query.getEndDate()
        );
    }
}