/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtils {

    public static String randomOrderNo(String prefix) {
        return prefix + DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + RandomUtil.randomNumbers(6);
    }

    public static BigDecimal divide100(BigDecimal decimal, BigDecimal defaultDecimal) {
        return Optional.ofNullable(decimal)
            .map(v -> v.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP))
            .orElse(defaultDecimal);
    }


    public static LocalDateTime convertTimezone(String fromTimeZone, String toTimeZone, LocalDateTime time) {
        ZoneId fromZoneId = ZoneId.of(fromTimeZone);
        ZoneId toZoneId = ZoneId.of(toTimeZone);

        // 创建一个ZonedDateTime对象，表示在GMT+0时区的当前日期时间
        ZonedDateTime fromZoneTime = time.atZone(fromZoneId);
        // 将GMT+0的日期时间转换为GMT+8时区的日期时间
        return fromZoneTime.withZoneSameInstant(toZoneId).toLocalDateTime();
    }

    public static boolean containChineseChar(String string) {
        if (StringUtils.isBlank(string)) {
            return false;
        }
        return string.matches(".*[\\u4e00-\\u9fa5]+.*");
    }

    public static boolean containsNonDigit(String str) {
        // 使用正则表达式判断是否包含非数字字符
        return str.matches(".*[^0-9].*");
    }

    public static String escapeMarkdown(String text) {
        if (text == null) {
            return null;
        }

        return text.replace("\\", "\\\\") // 转义反斜杠
            .replace("*", "\\*") // 转义星号
            .replace("_", "\\_") // 转义下划线
            .replace("{", "\\{") // 转义左花括号
            .replace("}", "\\}") // 转义右花括号
            .replace("[", "\\[") // 转义左方括号
            .replace("]", "\\]") // 转义右方括号
            .replace("(", "\\(") // 转义左圆括号
            .replace(")", "\\)") // 转义右圆括号
            .replace("#", "\\#") // 转义井号
            .replace("+", "\\+") // 转义加号
            .replace("-", "\\-") // 转义减号
            .replace("!", "\\!") // 转义感叹号
            .replace(".", "\\."); // 转义句号
    }

    public static List<String> extractEmails(String text) {
        // 正则表达式匹配邮箱
        String emailRegex = "[a-zA-Z0-9]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}";
        Pattern pattern = Pattern.compile(emailRegex);
        Matcher matcher = pattern.matcher(text);
        List<String> result = new ArrayList<>();
        while (matcher.find()) {
            System.out.println(matcher.group());
            result.add(matcher.group());
        }
        return result;
    }

    /**
     * 计算并调整给定的 LocalDateTime 列表，确保时间跨度至少为3天。
     *
     * @param dateTimes 时间点列表，不可为 null 或为空。
     * @return 包含[最早时间, 最晚时间]的 LocalDateTime 数组。
     * 如果原始跨度大于等于3天，则返回原始的最早和最晚时间。
     * 如果原始跨度小于3天，则将最早时间调整为最晚时间的3天前。
     * @throws IllegalArgumentException 如果列表为 null 或为空。
     */
    public static LocalDateTime[] getAdjustedTimeRange(List<LocalDateTime> dateTimes) {
        // 1. 健壮性检查：确保输入有效
        // if (dateTimes == null || dateTimes.isEmpty()) {
        //     throw new IllegalArgumentException("时间列表不能为空或为 null。");
        // }
        // 2. 查找最早和最晚时间（使用 Stream API 高效实现）
        LocalDateTime minDate = dateTimes.stream()
                .min(Comparator.naturalOrder())
                .orElseThrow()
                .toLocalDate().atStartOfDay();  // 转为当天的00:00:00
        LocalDateTime maxDate = dateTimes.stream()
                .max(Comparator.naturalOrder())
                .orElseThrow()
                .toLocalDate().atTime(23, 59, 59);
        // 3. 计算最早和最晚时间之间的天数差
        long daysBetween = Duration.between(minDate, maxDate).toDays();
        // 4. 根据天数差进行逻辑判断
        if (daysBetween >= 3) {
            return new LocalDateTime[]{minDate, maxDate};
        } else {
            LocalDateTime adjustedMinDate = maxDate.minusDays(2).toLocalDate().atStartOfDay();  // 调整后的最早时间也转为00:00:00
            return new LocalDateTime[]{adjustedMinDate, maxDate};
        }
    }

    public static String convertDaysToYMD(int days) {
        int years = days / 365; // 计算完整的年份
        days %= 365; // 剩余天数
        int months = days / 30; // 计算完整的月份
        days %= 30; // 剩余天数

        // 返回格式化的字符串
        String str = "";
        if (years > 0) {
            str = years + "年";
        }
        if (months > 0) {
            str += (months + "个月");
        }
        if (days > 0) {
            str += (days + "天");
        }
        return str;
    }
}
