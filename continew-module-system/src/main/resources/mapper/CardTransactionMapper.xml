<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CardTransactionMapper">
    <update id="updateCardNumber">
        UPDATE biz_card_transaction a
        SET a.card_number = COALESCE(
                (SELECT b.card_number FROM biz_card b WHERE b.platform = #{platform} AND b.platform_card_id = a.platform_card_id),
            ''
        )
        WHERE a.platform = #{platform} AND a.card_number = ''
    </update>

    <update id="updateAdAccountIdByTimeRange">
        UPDATE biz_card_transaction t
        SET t.ad_account_id = COALESCE(
                (SELECT c.platform_ad_id FROM biz_card c WHERE c.card_number = t.card_number),
                ''
                          )
        WHERE t.ad_account_id = ''
        AND t.china_time >= #{startTime}
        AND t.china_time &lt;= #{endTime}
    </update>
    
    <update id="updateCustomerIdByTimeRange">
        UPDATE biz_card_transaction t
        JOIN biz_ad_account_order o ON o.ad_account_id = t.ad_account_id and o.status in (3, 5)
        SET t.customer_id = o.customer_id
        WHERE t.ad_account_id != ''
        and t.customer_id is null
        and o.finish_time &lt;= t.stat_time
        AND IFNULL(o.recycle_time, CURRENT_TIME()) >= t.stat_time
        AND t.china_time >= #{startTime}
        AND t.china_time &lt;= #{endTime}
    </update>


    <select id="sumAmountByDateRange" resultType="java.math.BigDecimal">
        SELECT COALESCE(-SUM(t.trans_amount), 0) as total_amount
        FROM biz_card_transaction t
        WHERE t.ad_account_id = #{platformAdId}
          AND t.stat_time >= #{startTime}
          AND t.stat_time &lt;= #{endTime}
          AND trans_status != 4
    </select>


    <!-- 查询交易汇总数据 -->
    <select id="selectTransactionSummary" resultType="top.continew.admin.biz.model.resp.CardTransactionSummaryResp">
        SELECT COALESCE(-SUM(t.trans_amount), 0) as totalSpend
        FROM biz_card_transaction t
        WHERE t.trans_status != 4
        <if test="platform != null">
            AND t.platform = #{platform}
        </if>
        <if test="startTime != null">
            AND t.china_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND t.china_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询卡片汇总数据 -->
    <select id="selectCardSummary" resultType="top.continew.admin.biz.model.resp.CardTransactionSummaryResp">
        SELECT COUNT(*) as totalCardCount,
               COALESCE(SUM(balance), 0) as totalBalance
        FROM biz_card c
        WHERE 1=1
        <if test="platform != null">
            AND c.platform = #{platform}
        </if>
        <if test="startTime != null">
            AND c.open_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND c.open_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询每日统计 -->
    <select id="selectStatisticsByDatePage" resultType="top.continew.admin.biz.model.resp.CardTransactionStatByDateResp">
        SELECT DATE(t.china_time) as date,
               -COALESCE(SUM(t.trans_amount), 0) as spend
        FROM biz_card_transaction t
        WHERE t.trans_status != 4
        <if test="platform != null">
            AND t.platform = #{platform}
        </if>
        <if test="startTime != null">
            AND t.china_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND t.china_time &lt;= #{endTime}
        </if>
        GROUP BY DATE(t.china_time)
        ORDER BY date DESC
    </select>
    <select id="selectDashboardOverviewSpent"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp">
        SELECT IFNULL(-SUM(CASE
                               WHEN tr.trans_status != 4
                                   THEN tr.trans_amount
            END), 0)        AS total,
               IFNULL(-SUM(CASE
                               WHEN tr.stat_time >= CURDATE()
                                   AND tr.stat_time &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY)
                                   AND tr.trans_status != 4
                                   THEN tr.trans_amount
                   END), 0) AS today,
               IFNULL(-SUM(CASE
                               WHEN tr.stat_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                                   AND tr.stat_time &lt; CURDATE()
                                   AND tr.trans_status != 4
                                   THEN tr.trans_amount
                   END), 0) AS yesterday
        FROM biz_card_transaction tr
        where customer_id is not null
          and customer_id not in (select id from biz_customer where is_self_account = true)
    </select>
    <select id="selectListDashboardAnalysisSpent"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        SELECT
        DATE_FORMAT(stat_time, '%Y-%m') AS name,
        ifnull(-sum(trans_amount), 0) AS value
        FROM biz_card_transaction
        WHERE trans_status != 4 and customer_id is not null
        and customer_id not in (select id from biz_customer where is_self_account = true) AND DATE_FORMAT(stat_time,
        '%Y-%m') IN
        <foreach collection="months" item="month" separator="," open="(" close=")">
            #{month}
        </foreach>
        GROUP BY name
        ORDER BY name
    </select>
    <select id="selectStatisticsByCardholderPage"
            resultType="top.continew.admin.biz.model.resp.CardTransactionStatByCardholderResp">
        SELECT c.platform_card_holder_id as username,
        COUNT(DISTINCT c.card_number) as cardCount,
        COALESCE(SUM(c.balance), 0) as balance,
        SUM(COALESCE(
        (SELECT -SUM(tt.trans_amount)
        FROM biz_card_transaction tt
        WHERE tt.card_number = c.card_number
        AND tt.trans_status != 4
        <if test="startTime != null">
            AND tt.stat_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND tt.stat_time &lt;= #{endTime}
        </if>
        ), 0
        )) as totalSpend
        FROM biz_card c
        <where>
            <if test="platform != null">
                AND c.platform = #{platform}
            </if>
            <if test="startTime != null">
                AND c.open_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND c.open_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY username
    </select>
    <select id="selectStatisticsByTimezonePage"
            resultType="top.continew.admin.biz.model.resp.CardTransactionStatByTimezoneResp">
        SELECT a.timezone,
        COALESCE(-SUM(t.trans_amount), 0) as spend
        FROM biz_card_transaction t left join biz_ad_account a on a.platform_ad_id = t.ad_account_id
        WHERE t.trans_status != 4 AND t.ad_account_id != '' and a.timezone != ''
        <if test="platform != null">
            AND t.platform = #{platform}
        </if>
        <if test="startTime != null">
            AND t.stat_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND t.stat_time &lt;= #{endTime}
        </if>
        GROUP BY a.timezone
        ORDER BY spend desc

    </select>
    <select id="selectStatisticsByDateList"
            resultType="top.continew.admin.biz.model.resp.CardTransactionStatByDateResp">
        SELECT DATE_FORMAT(t.china_time, '%Y-%m-%d') as date,
        -COALESCE(SUM(t.trans_amount), 0) as spend
        FROM biz_card_transaction t
        WHERE t.trans_status != 4
        <if test="platform != null">
            AND t.platform = #{platform}
        </if>
        <if test="startTime != null">
            AND t.china_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND t.china_time &lt;= #{endTime}
        </if>
        GROUP BY DATE_FORMAT(t.china_time, '%Y-%m-%d')
        ORDER BY date DESC
    </select>
    <select id="getCustomerAdAccountDailySpent"
            resultType="top.continew.admin.biz.model.resp.CustomerAdAccountCardSpentResp">
        select customer_id, ad_account_id as platform_ad_id, COALESCE(-SUM(trans_amount), 0) as card_spent
        from biz_card_transaction
        where DATE(china_time) = #{statDate}
          and trans_status != 4
          and customer_id is not null
        group by customer_id, ad_account_id
    </select>

</mapper>