/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import top.continew.admin.biz.config.TelegramChatIdConfig;
import top.continew.admin.biz.enums.AdAccountKeepStatusEnum;
import top.continew.admin.biz.enums.CardStatusEnum;
import top.continew.admin.biz.event.AdsPowerEnvEvent;
import top.continew.admin.biz.event.TelegramMessageEvent;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.req.AdsPowerActiveReq;
import top.continew.admin.biz.model.req.AdsPowerOpenReq;
import top.continew.admin.biz.model.req.AnalyzeForRemoveUsersReq;
import top.continew.admin.biz.model.req.BrowserReq;
import top.continew.admin.biz.report.ReportOpsStrategyFactory;
import top.continew.admin.biz.report.strategy.ReportOpsStrategy;
import top.continew.admin.biz.service.*;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;
import top.continew.starter.cache.redisson.util.RedisUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.log.annotation.Log;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Date;
import java.util.List;

import static top.continew.admin.common.constant.CacheConstants.BROWSER_ACTIVE_KEY_PREFIX;

@Log(ignore = true)
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class ApiController {

    private final UserService userService;

    private final AdAccountBrowserLogService adAccountBrowserLogService;

    private final ReportOpsStrategyFactory reportOpsStrategyFactory;

    private final RechargeOrderService rechargeOrderService;

    private final AdAccountService adAccountService;

    private final ClearOrderService clearOrderService;

    private final RefundOrderService refundOrderService;

    private final CardTransactionService cardTransactionService;

    private final TelegramChatIdConfig telegramChatIdConfig;

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    private final CardService cardService;

    private final AdsPowerService adsPowerService;

    private final AdAccountCardService adAccountCardService;

    @PostMapping("/report")
    public void report(@Validated @RequestBody BrowserReq browserReq) {

        UserDO user = userService.getByUsername(browserReq.getActiveCode());
        if (user == null) {
            throw new BusinessException(browserReq.getActiveCode() + "用户不存在");
        }
        ReportOpsStrategy strategy = reportOpsStrategyFactory.findStrategy(browserReq.getName());
        if (strategy != null) {
            String result = strategy.handle(browserReq, user);
            if (StringUtils.isNotBlank(result)) {
                log.info("【{}】{}", strategy.getReport().getLabel(), result);
                AdAccountBrowserLogDO adAccountBrowserLogDO = new AdAccountBrowserLogDO();
                adAccountBrowserLogDO.setName(browserReq.getName());
                adAccountBrowserLogDO.setData(result);
                adAccountBrowserLogDO.setLabel(browserReq.getLabel());
                adAccountBrowserLogDO.setOpsTime(browserReq.getTs());
                adAccountBrowserLogDO.setPlatformAccountId(browserReq.getFbAccountId());
                adAccountBrowserLogDO.setActiveCode(browserReq.getActiveCode());
                adAccountBrowserLogDO.setEnv(browserReq.getEnv().toJSONString());
                adAccountBrowserLogDO.setCreateUser(user.getId());
                if (JSON.isValidObject(result)) {
                    JSONObject jsonObject = JSON.parseObject(result);
                    if (jsonObject.containsKey("platformAdId")) {
                        adAccountBrowserLogDO.setPlatformAdId(jsonObject.getString("platformAdId"));
                    }
                }
                adAccountBrowserLogService.save(adAccountBrowserLogDO);
            }
        }
    }

    @PostMapping("checkModifyLimit")
    public void checkModifyLimit(@RequestBody Object data) {
        JSONObject jsonObject = JSONObject.from(data);
        log.info("【广告户限额检测】{}", jsonObject.toString());
        String platformAdId = jsonObject.getString("billable_account_payment_legacy_account_id");
        JSONObject newSpendLimit = jsonObject.getJSONObject("new_spend_limit");
        BigDecimal newLimitAmount = newSpendLimit.getBigDecimal("amount");
        //        JSONObject loggingData = jsonObject.getJSONObject("logging_data");
        //        String loggingId = loggingData.getString("logging_id");
        String loggingId = "";
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(platformAdId);
        CheckUtils.throwIfNull(adAccount, "广告户不存在");
        if (newLimitAmount.compareTo(new BigDecimal("0.01")) == 0) {
            clearOrderService.checkModifyLimit(platformAdId, loggingId);
        } else {
            if (newLimitAmount.compareTo(new BigDecimal(20)) <= 0) {
                adAccountService.update(Wrappers.<AdAccountDO>lambdaUpdate()
                    .set(AdAccountDO::getKeepStatus, AdAccountKeepStatusEnum.PROCESS)
                    .eq(AdAccountDO::getPlatformAdId, platformAdId)
                    .eq(AdAccountDO::getKeepStatus, AdAccountKeepStatusEnum.LOGIN_SUCCESS));
                return;
            }
            if (newLimitAmount.compareTo(adAccount.getSpendCap()) > 0) {
                rechargeOrderService.checkModifyLimit(platformAdId, newLimitAmount, adAccount.getSpendCap(), loggingId);
            } else {
                refundOrderService.checkModifyLimit(platformAdId, newLimitAmount, adAccount.getSpendCap(), loggingId);
            }
        }
    }

    @PostMapping("/cv/callback")
    public void cvCallback(@RequestBody JSONObject data) {
        cardTransactionService.syncCardVpByCallback(data);
    }

    @PostMapping("browser/active")
    public void browserActiveReport(@RequestBody AdsPowerActiveReq req) {
        if (CollUtil.isNotEmpty(req.getActiveEnvs()) && StringUtils.isNotBlank(req.getActiveCode())) {
            for (AdsPowerActiveReq.AdsPowerActiveItemReq serialNumber : req.getActiveEnvs()) {
                if (serialNumber.getEnvId().length() > 10) {
                    RedisUtils.set(BROWSER_ACTIVE_KEY_PREFIX + serialNumber.getEnvId(), req.getActiveCode(), Duration.ofSeconds(5));
                } else {
                    RedisUtils.set(BROWSER_ACTIVE_KEY_PREFIX + serialNumber.getEnvId(), req.getActiveCode(), Duration.ofSeconds(5));
                    RedisUtils.set(BROWSER_ACTIVE_KEY_PREFIX + serialNumber.getSerial_number(), req.getActiveCode(), Duration.ofSeconds(5));
                }
            }
        }
    }

    @PostMapping("browser/open")
    public void browserOpenReport(@RequestBody List<AdsPowerOpenReq> req) {
        for (AdsPowerOpenReq adsPowerOpenReq : req) {
            AdsPowerEnvEvent event = new AdsPowerEnvEvent(adsPowerOpenReq);
            SpringUtil.publishEvent(event);
        }
    }

    @PostMapping("sendUpgradeMessage")
    public void sendUpgradeMessage(@RequestBody String body) {
        String title = "######\t%s更新公告\t######".formatted(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm"));
        String message = title + "\n\n" + body;
        SendMessage sendMessage = SendMessage.builder()
            .chatId(telegramChatIdConfig.getDevelopmentChatId())
            .text(message)
            .build();
        SpringUtil.publishEvent(new TelegramMessageEvent(sendMessage));
    }

    @GetMapping("withdraw")
    public void withdraw(String cardNumber, Boolean inactive) {
        if (inactive == null) {
            inactive = false;
        }
        CardDO cardDO = cardService.getByCardNumber(cardNumber, false);
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(cardDO.getPlatform());
        cardOpsStrategy.withdrawCard(cardDO, null);
        if (cardDO.getStatus().equals(CardStatusEnum.NORMAL) && inactive) {
            cardOpsStrategy.inactiveCard(cardDO);
        }
    }

    @GetMapping("withdraw1")
    public void withdraw1(String platformAdId) {
        AdAccountDO adAccount = adAccountService.getByPlatformAdId(platformAdId);
        AdAccountCardDO adAccountCardDO = adAccountCardService.getDefaultCard(adAccount.getPlatformAdId());
        if (adAccountCardDO == null) {
            return;
        }
        CardDO cardDO = cardService.getByCardNumber(adAccountCardDO.getFullCardNumber(), false);
        if (cardDO == null) {
            return;
        }
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(cardDO.getPlatform());
        BigDecimal fbBalance = adAccount.getSpendCap().subtract(adAccount.getAmountSpent());
        BigDecimal cardBalance = cardOpsStrategy.getCardBalance(cardDO);
        if (cardBalance.compareTo(fbBalance) > 0) {
            BigDecimal reduceBalance = cardBalance.subtract(fbBalance);
            cardOpsStrategy.withdrawCard(cardDO, reduceBalance);
        }
    }

    @PostMapping("analyzeForRemoveUsers")
    public List<AnalyzeForRemoveUsersReq> analyzeForRemoveUsers(@RequestBody List<AnalyzeForRemoveUsersReq> req) {
        for (AnalyzeForRemoveUsersReq analyzeForRemoveUsersReq : req) {
            for (AnalyzeForRemoveUsersReq.AnalyzeForRemoveUsersItemReq user : analyzeForRemoveUsersReq.getUsers()) {
                boolean exist = adsPowerService.exists(Wrappers.<AdsPowerDO>lambdaQuery()
                    .like(AdsPowerDO::getRemark, user.getId()));
                user.setNeedDelete(!exist);
            }
        }
        return req;
    }
}
