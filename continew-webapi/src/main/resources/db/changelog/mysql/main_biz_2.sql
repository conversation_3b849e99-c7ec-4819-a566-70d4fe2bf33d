-- liquibase formatted sql

-- changeset yqx:10000
-- comment 客户新增用户名密码
ALTER TABLE `biz_customer`
    ADD COLUMN `username` varchar(255) NULL COMMENT '用户名' AFTER `product_name`,
    ADD COLUMN `password` varchar(255) NULL COMMENT '密码' AFTER `username`;

-- changeset yqx:10001
-- comment 新增客户邮箱
ALTER TABLE `biz_customer_requirement`
    ADD COLUMN `customer_email` varchar(255) NULL COMMENT '客户邮箱' AFTER `ad_account_name`;
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `customer_email` varchar(255) NULL COMMENT '客户邮箱' AFTER `is_one_dollar`;
-- changeset yqx:10002
-- comment 接收状态
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `take_status` bit(1) NOT NULL DEFAULT b'0' COMMENT '接收状态' AFTER `customer_email`;

-- changeset yqx:10003
-- comment 新建表
CREATE TABLE `biz_customer_order_group`
(
    `id`          bigint NOT NULL COMMENT 'ID',
    `name`        varchar(255) DEFAULT NULL COMMENT '名称',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `customer_id` bigint       DEFAULT NULL COMMENT '客户ID',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='客户下户订单分组';
ALTER TABLE `biz_ad_account_order`
    ADD COLUMN `group_id` bigint NULL COMMENT '客户分组ID' AFTER `take_status`;


-- changeset hans:10001
-- comment 新建表
alter table biz_ad_account_order
    add clear_status int NOT NULL DEFAULT 1 comment '清零状态';

alter table biz_ad_account_order
    add clear_time datetime null comment '清零时间';

alter table biz_clear_order
    add ad_account_order_id bigint null comment '关联下户订单';


-- changeset hans:10002
-- comment 新增字段
alter table biz_purchase_order
    add receive_date date null comment '验收时间' after receive_price;


-- changeset yqx:10004
-- comment 新增字段
ALTER TABLE `sys_user`
    ADD COLUMN `job_rank` int NULL COMMENT '岗位职级',
    ADD COLUMN `telegram_id` int NULL COMMENT '飞机号';


-- changeset yqx:10005
-- comment 新增字段
ALTER TABLE `biz_customer`
    ADD COLUMN `cooperate_time` datetime NULL COMMENT '合作时间';

-- changeset yqx:10006
-- comment 新增字段
CREATE TABLE `biz_sales_daily_summary`
(
    `id`          BIGINT   NOT NULL COMMENT 'ID',
    `record_date` DATE     NOT NULL COMMENT '日报记录的日期',
    `content`     TEXT     NOT NULL COMMENT '日报具体内容，支持长文本',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `create_user` bigint   NOT NULL COMMENT '创建人',
    `update_time` datetime NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='商务日报';