worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;
    # 限制 body 大小
    client_max_body_size 100m;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    # 后端项目
    upstream admin-server {
        ip_hash;
        server **********:18000;
    }

    server {
        listen       443 ssl;
        server_name  api.continew.top;

        # 证书直接存放 /docker/nginx/cert 目录下即可（更改证书名称即可，无需更改证书路径）
        ssl_certificate      /etc/nginx/cert/xxx.local.pem; # /etc/nginx/cert/ 为 docker 映射路径 不允许更改
        ssl_certificate_key  /etc/nginx/cert/xxx.local.key; # /etc/nginx/cert/ 为 docker 映射路径 不允许更改
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;

        location / {
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_ignore_client_abort on;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://admin-server/;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

    # HTTP 请求 将转发到 HTTPS
    server {
        listen  80;
        server_name  api.continew.top;
        rewrite ^ https://$http_host$request_uri? permanent;
    }

    # 前端项目
    server {
        listen       443 ssl;
        server_name  admin.continew.top;

        # 证书直接存放 /docker/nginx/cert 目录下即可（更改证书名称即可，无需更改证书路径）
        ssl_certificate      /etc/nginx/cert/xxx.local.pem; # /etc/nginx/cert/ 为 docker 映射路径 不允许更改
        ssl_certificate_key  /etc/nginx/cert/xxx.local.key; # /etc/nginx/cert/ 为 docker 映射路径 不允许更改
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;

        location / {
            root   /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            index  index.html index.htm;
            # Nginx 部署时，POST 请求本地静态文件会返回 405 错误（Not Allowed）
            # 用于解决一半 mock 数据，一半后端接口的情况
            error_page 405 =200 https://$host$request_uri;
        }

        # /api/ 代理到后端（如果使用 /api/ 前缀代理而不使用 api 域名提供后端服务，可放开此配置）
        #location /api/ {
        #    proxy_http_version 1.1;
        #    proxy_set_header Upgrade $http_upgrade;
        #    proxy_set_header Connection "upgrade";
        #    proxy_ignore_client_abort on;
        #    proxy_set_header Host $http_host;
        #    proxy_set_header X-Real-IP $remote_addr;
        #    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #    proxy_pass http://admin-server/;
        #}

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

    # 将 HTTP 请求转发到 HTTPS
    server {
        listen  80;
        server_name  admin.continew.top;
        rewrite ^ https://$http_host$request_uri? permanent;
    }
}
